import React, { useState } from 'react';
import { StockManager, StockMonitorPanel } from '@/components';
import { useStockList } from '@/hooks/useStockData';
import { X, List, ChevronUp, ChevronDown, Activity } from 'lucide-react';

interface SidebarProps {
  /** 选中的股票代码 */
  selectedStock: string | null;
  /** 股票选择事件 */
  onStockSelect: (code: string) => void;
  /** 是否显示侧边栏（移动端） */
  isOpen?: boolean;
  /** 关闭侧边栏事件（移动端） */
  onClose?: () => void;
  /** 自定义类名 */
  className?: string;
}

/**
 * 侧边栏组件
 */
export const Sidebar: React.FC<SidebarProps> = ({
  selectedStock,
  onStockSelect,
  isOpen = true,
  onClose,
  className = '',
}) => {
  const { stocks, error } = useStockList();
  const [isMonitorExpanded, setIsMonitorExpanded] = useState(true);

  // 处理监控面板中的股票点击事件
  const handleMonitorStockClick = (stockCode: string) => {
    onStockSelect(stockCode);
  };

  // 切换监控面板展开状态
  const toggleMonitorExpanded = () => {
    setIsMonitorExpanded(!isMonitorExpanded);
  };



  return (
    <>
      {/* 移动端遮罩层 */}
      {isOpen && onClose && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={onClose}
        />
      )}

      {/* 侧边栏内容 */}
      <aside 
        className={`
          fixed lg:static inset-y-0 left-0 z-50 lg:z-auto
          w-80 bg-white border-r border-gray-200
          transform transition-transform duration-300 ease-in-out
          ${isOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}
          ${className}
        `}
      >
        {/* 侧边栏头部 */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 lg:hidden">
          <h2 className="text-lg font-semibold text-gray-900">股票管理</h2>
          {onClose && (
            <button
              onClick={onClose}
              className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
              aria-label="关闭菜单"
            >
              <X className="w-5 h-5" />
            </button>
          )}
        </div>

        {/* 侧边栏内容 */}
        <div className="flex flex-col h-full lg:h-auto">
          {/* 股票管理组件 */}
          <div className="flex-1 p-4">
            <StockManager
              selectedStock={selectedStock}
              onSelectStock={onStockSelect}
            />
          </div>



          {/* 实时监控面板 */}
          <div className="border-t border-gray-200 bg-gray-50">
            <div className="p-4">
              {/* 监控面板头部 */}
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-2">
                  <Activity className="w-4 h-4 text-blue-600" />
                  <h3 className="text-sm font-medium text-gray-900">实时监控</h3>
                  <span className="text-xs text-gray-500">({stocks.length}只)</span>
                </div>
                <button
                  onClick={toggleMonitorExpanded}
                  className="p-1 hover:bg-gray-200 rounded transition-colors"
                  title={isMonitorExpanded ? '收起监控面板' : '展开监控面板'}
                >
                  {isMonitorExpanded ? (
                    <ChevronUp className="w-4 h-4 text-gray-600" />
                  ) : (
                    <ChevronDown className="w-4 h-4 text-gray-600" />
                  )}
                </button>
              </div>

              {/* 当前选中股票信息 */}
              {selectedStock && (
                <div className="mb-3 p-2 bg-blue-50 border border-blue-200 rounded text-xs">
                  <div className="flex items-center gap-2 text-blue-700">
                    <List className="w-3 h-3" />
                    <span>当前选中: {selectedStock}</span>
                  </div>
                </div>
              )}

              {/* 错误信息显示 */}
              {error && (
                <div className="mb-3 p-2 bg-red-50 border border-red-200 rounded text-xs text-red-600">
                  加载失败: {error.message}
                </div>
              )}

              {/* 监控面板内容 */}
              {isMonitorExpanded && (
                <div className="space-y-2">
                  <StockMonitorPanel
                    compact={true}
                    maxItems={15}
                    onStockClick={handleMonitorStockClick}
                    className="max-h-80 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100"
                  />
                </div>
              )}

              {/* 收起状态的简要信息 */}
              {!isMonitorExpanded && stocks.length > 0 && (
                <div className="text-xs text-gray-500 text-center py-2">
                  点击展开查看 {stocks.length} 只股票的实时监控
                </div>
              )}
            </div>
          </div>
        </div>
      </aside>
    </>
  );
};
