import { useState, useCallback, useEffect } from 'react';
import { Stock } from '@/types';
import { validateStockCode, formatStockCode, isDuplicateCode } from '@/utils/validation';

// 常见股票名称映射
const STOCK_NAME_MAP: Record<string, string> = {
  '000001': '平安银行',
  '000002': '万科A',
  '000858': '五粮液',
  '600036': '招商银行',
  '600121': '郑州煤电',
  '600519': '贵州茅台',
  '002415': '海康威视',
  '600276': '恒瑞医药',
  '000725': '京东方A',
  '600793': '宜宾纸业',
  '603067': '振华股份',
  // 可以继续添加更多股票
};

/**
 * 获取股票名称
 * @param code 股票代码
 * @returns 股票名称
 */
function getStockName(code: string): string {
  return STOCK_NAME_MAP[code] || `股票${code}`;
}

interface UseStockListReturn {
  stocks: Stock[];
  addStock: (code: string, name?: string) => Promise<{ success: boolean; message?: string }>;
  removeStock: (code: string) => void;
  clearAllStocks: () => void;
  isLoading: boolean;
  error: string | null;
}

const STORAGE_KEY = 'gupiao-stock-list';

/**
 * 股票列表管理Hook
 */
export function useStockList(): UseStockListReturn {
  const [stocks, setStocks] = useState<Stock[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 从localStorage加载股票列表
  useEffect(() => {
    try {
      const savedStocks = localStorage.getItem(STORAGE_KEY);
      if (savedStocks) {
        const parsedStocks = JSON.parse(savedStocks);
        setStocks(parsedStocks);
      }
    } catch (err) {
      console.error('加载股票列表失败:', err);
      setError('加载股票列表失败');
    }
  }, []);

  // 保存股票列表到localStorage
  const saveToStorage = useCallback((stockList: Stock[]) => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(stockList));
    } catch (err) {
      console.error('保存股票列表失败:', err);
      setError('保存股票列表失败');
    }
  }, []);

  // 添加股票
  const addStock = useCallback(async (code: string, name?: string): Promise<{ success: boolean; message?: string }> => {
    setIsLoading(true);
    setError(null);

    try {
      // 验证股票代码
      const validation = validateStockCode(code);
      if (!validation.isValid) {
        return { success: false, message: validation.message };
      }

      const formattedCode = formatStockCode(code);
      const existingCodes = stocks.map(stock => stock.code);

      // 检查重复
      if (isDuplicateCode(formattedCode, existingCodes)) {
        return { success: false, message: '股票代码已存在' };
      }

      // 使用传入的名称，如果没有则使用映射表或默认格式
      const stockName = name || getStockName(formattedCode);

      // 创建新股票对象
      const newStock: Stock = {
        code: formattedCode,
        name: stockName,
      };

      // 更新股票列表
      const updatedStocks = [...stocks, newStock];
      setStocks(updatedStocks);
      saveToStorage(updatedStocks);

      return { success: true, message: '股票添加成功' };
    } catch (err) {
      const errorMessage = '添加股票失败';
      setError(errorMessage);
      return { success: false, message: errorMessage };
    } finally {
      setIsLoading(false);
    }
  }, [stocks, saveToStorage]);

  // 删除股票
  const removeStock = useCallback((code: string) => {
    setError(null);
    
    try {
      const updatedStocks = stocks.filter(stock => stock.code !== code);
      setStocks(updatedStocks);
      saveToStorage(updatedStocks);
    } catch (err) {
      console.error('删除股票失败:', err);
      setError('删除股票失败');
    }
  }, [stocks, saveToStorage]);

  // 清空所有股票
  const clearAllStocks = useCallback(() => {
    setError(null);
    
    try {
      setStocks([]);
      saveToStorage([]);
    } catch (err) {
      console.error('清空股票列表失败:', err);
      setError('清空股票列表失败');
    }
  }, [saveToStorage]);

  return {
    stocks,
    addStock,
    removeStock,
    clearAllStocks,
    isLoading,
    error,
  };
}
