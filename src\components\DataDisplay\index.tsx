import React, { useState } from 'react';
import { FlowChart } from './FlowChart';
import { DataTable } from './DataTable';
import { useStockData } from '@/hooks/useStockData';
import { formatMoneyAuto, getFlowColor, getRelativeTime } from '@/utils/formatters';
import { BarChart3, Table, RefreshCw, AlertCircle, TrendingUp, TrendingDown } from 'lucide-react';

interface DataDisplayProps {
  /** 股票代码 */
  stockCode: string;
  /** 数据条数限制 */
  limit?: number;
  /** 自定义类名 */
  className?: string;
}

/**
 * 数据展示主组件
 */
export const DataDisplay: React.FC<DataDisplayProps> = ({
  stockCode,
  limit = 240,
  className = '',
}) => {
  const [activeTab, setActiveTab] = useState<'chart' | 'table'>('chart');
  
  // 获取股票数据
  const {
    summary,
    klines,
    isLoading,
    isError,
    error,
    refetch,
    isFetching,
  } = useStockData(stockCode, limit, {
    enabled: !!stockCode,
  });

  // 渲染加载状态
  if (isLoading) {
    return (
      <div className={`bg-white rounded-lg border p-8 ${className}`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
          <p className="text-gray-600 font-medium">正在加载股票数据...</p>
          <p className="text-gray-500 text-sm mt-1">股票代码: {stockCode}</p>
        </div>
      </div>
    );
  }

  // 渲染错误状态
  if (isError) {
    return (
      <div className={`bg-white rounded-lg border p-8 ${className}`}>
        <div className="text-center">
          <div className="text-red-500 mb-4">
            <AlertCircle className="w-12 h-12 mx-auto" />
          </div>
          <p className="text-red-600 font-medium mb-2">数据加载失败</p>
          <p className="text-gray-500 text-sm mb-4">
            {error?.message || '无法获取股票数据，请稍后重试'}
          </p>
          <button
            onClick={() => refetch()}
            className="btn btn-primary flex items-center gap-2 mx-auto"
          >
            <RefreshCw className="w-4 h-4" />
            重新加载
          </button>
        </div>
      </div>
    );
  }

  // 渲染无数据状态
  if (!summary && klines.length === 0) {
    return (
      <div className={`bg-white rounded-lg border p-8 ${className}`}>
        <div className="text-center">
          <div className="text-gray-400 mb-4">
            <BarChart3 className="w-12 h-12 mx-auto" />
          </div>
          <p className="text-gray-600 font-medium">暂无数据</p>
          <p className="text-gray-500 text-sm mt-1">
            股票代码 {stockCode} 暂无资金流向数据
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* 数据概览卡片 */}
      {summary && (
        <div className="bg-white rounded-lg border p-6">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h2 className="text-xl font-bold text-gray-900">
                {summary.name}
              </h2>
              <p className="text-gray-500">
                {summary.code} · 最后更新: {getRelativeTime(summary.lastUpdate)}
              </p>
            </div>
            
            {isFetching && (
              <div className="flex items-center gap-2 text-primary-600">
                <RefreshCw className="w-4 h-4 animate-spin" />
                <span className="text-sm">更新中...</span>
              </div>
            )}
          </div>
          
          {/* 资金流向概览 */}
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <div className="text-xs text-gray-500 mb-1">主力净流入</div>
              <div 
                className="text-lg font-bold"
                style={{ color: getFlowColor(summary.mainNetInflow) }}
              >
                {formatMoneyAuto(summary.mainNetInflow)}
              </div>
              <div className="flex items-center justify-center mt-1">
                {summary.mainNetInflow >= 0 ? (
                  <TrendingUp className="w-3 h-3 text-red-500" />
                ) : (
                  <TrendingDown className="w-3 h-3 text-green-500" />
                )}
              </div>
            </div>
            
            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <div className="text-xs text-gray-500 mb-1">超大单净流入</div>
              <div 
                className="text-lg font-bold"
                style={{ color: getFlowColor(summary.superLargeNetInflow) }}
              >
                {formatMoneyAuto(summary.superLargeNetInflow)}
              </div>
              <div className="flex items-center justify-center mt-1">
                {summary.superLargeNetInflow >= 0 ? (
                  <TrendingUp className="w-3 h-3 text-red-500" />
                ) : (
                  <TrendingDown className="w-3 h-3 text-green-500" />
                )}
              </div>
            </div>
            
            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <div className="text-xs text-gray-500 mb-1">大单净流入</div>
              <div 
                className="text-lg font-bold"
                style={{ color: getFlowColor(summary.largeNetInflow) }}
              >
                {formatMoneyAuto(summary.largeNetInflow)}
              </div>
              <div className="flex items-center justify-center mt-1">
                {summary.largeNetInflow >= 0 ? (
                  <TrendingUp className="w-3 h-3 text-red-500" />
                ) : (
                  <TrendingDown className="w-3 h-3 text-green-500" />
                )}
              </div>
            </div>
            
            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <div className="text-xs text-gray-500 mb-1">中单净流入</div>
              <div 
                className="text-lg font-bold"
                style={{ color: getFlowColor(summary.mediumNetInflow) }}
              >
                {formatMoneyAuto(summary.mediumNetInflow)}
              </div>
              <div className="flex items-center justify-center mt-1">
                {summary.mediumNetInflow >= 0 ? (
                  <TrendingUp className="w-3 h-3 text-red-500" />
                ) : (
                  <TrendingDown className="w-3 h-3 text-green-500" />
                )}
              </div>
            </div>
            
            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <div className="text-xs text-gray-500 mb-1">小单净流入</div>
              <div 
                className="text-lg font-bold"
                style={{ color: getFlowColor(summary.smallNetInflow) }}
              >
                {formatMoneyAuto(summary.smallNetInflow)}
              </div>
              <div className="flex items-center justify-center mt-1">
                {summary.smallNetInflow >= 0 ? (
                  <TrendingUp className="w-3 h-3 text-red-500" />
                ) : (
                  <TrendingDown className="w-3 h-3 text-green-500" />
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 标签页切换 */}
      <div className="bg-white rounded-lg border">
        <div className="border-b">
          <nav className="flex">
            <button
              onClick={() => setActiveTab('chart')}
              className={`px-6 py-3 text-sm font-medium border-b-2 transition-colors ${
                activeTab === 'chart'
                  ? 'border-primary-600 text-primary-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <BarChart3 className="w-4 h-4 inline mr-2" />
              图表分析
            </button>
            <button
              onClick={() => setActiveTab('table')}
              className={`px-6 py-3 text-sm font-medium border-b-2 transition-colors ${
                activeTab === 'table'
                  ? 'border-primary-600 text-primary-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <Table className="w-4 h-4 inline mr-2" />
              数据明细
            </button>
          </nav>
        </div>

        {/* 标签页内容 */}
        <div className="p-6">
          {activeTab === 'chart' && (
            <FlowChart
              klines={klines}
              summary={summary}
              type="line"
              height={500}
              showToolbar={true}
              loading={isFetching}
              error={error?.message || null}
            />
          )}
          
          {activeTab === 'table' && (
            <DataTable
              klines={klines}
              summary={summary}
              pageSize={20}
              showSearch={true}
              showExport={true}
            />
          )}
        </div>
      </div>
    </div>
  );
};

// 导出组件
export { FlowChart } from './FlowChart';
export { DataTable } from './DataTable';
export { MiniFlowChart } from './MiniFlowChart';
