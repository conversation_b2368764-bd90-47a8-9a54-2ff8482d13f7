import { Trash2, TrendingUp, Alert<PERSON>riangle, ExternalLink } from 'lucide-react';
import { Stock } from '@/types';

interface StockListProps {
  stocks: Stock[];
  onRemoveStock: (code: string) => void;
  onSelectStock?: (code: string) => void;
  selectedStock?: string | null;
}

export function StockList({ 
  stocks, 
  onRemoveStock, 
  onSelectStock,
  selectedStock 
}: StockListProps) {
  if (stocks.length === 0) {
    return (
      <div className="text-center py-8">
        <TrendingUp className="w-12 h-12 text-gray-300 mx-auto mb-3" />
        <p className="text-gray-500 mb-2">暂无股票代码</p>
        <p className="text-sm text-gray-400">请添加股票代码开始监控</p>
      </div>
    );
  }

  return (
    <div className="space-y-2">
      {/* 列表头部 */}
      <div className="flex items-center justify-between text-sm text-gray-600 pb-2 border-b border-gray-200">
        <span>股票代码 ({stocks.length})</span>
        <span>操作</span>
      </div>
      
      {/* 股票列表 */}
      <div className="space-y-1 max-h-64 overflow-y-auto">
        {stocks.map((stock) => (
          <StockListItem
            key={stock.code}
            stock={stock}
            isSelected={selectedStock === stock.code}
            onSelect={onSelectStock}
            onRemove={onRemoveStock}
          />
        ))}
      </div>
      
      {/* 批量操作 */}
      {stocks.length > 1 && (
        <div className="pt-3 border-t border-gray-200">
          <button
            onClick={() => {
              if (window.confirm('确定要清空所有股票代码吗？')) {
                stocks.forEach(stock => onRemoveStock(stock.code));
              }
            }}
            className="text-sm text-danger-600 hover:text-danger-700 flex items-center gap-1"
          >
            <AlertTriangle className="w-4 h-4" />
            清空所有
          </button>
        </div>
      )}
    </div>
  );
}

interface StockListItemProps {
  stock: Stock;
  isSelected?: boolean;
  onSelect?: (code: string) => void;
  onRemove: (code: string) => void;
}

function StockListItem({ stock, isSelected, onSelect, onRemove }: StockListItemProps) {
  const handleRemove = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (window.confirm(`确定要删除股票 ${stock.code} 吗？`)) {
      onRemove(stock.code);
    }
  };

  const handleSelect = () => {
    if (onSelect) {
      onSelect(stock.code);
    }
  };

  const handleViewFlow = (e: React.MouseEvent) => {
    e.stopPropagation();
    const url = `https://data.eastmoney.com/zjlx/${stock.code}.html`;
    window.open(url, '_blank');
  };

  return (
    <div
      className={`
        flex items-center justify-between p-3 rounded-lg border transition-all duration-200
        ${isSelected 
          ? 'border-primary-500 bg-primary-50' 
          : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
        }
        ${onSelect ? 'cursor-pointer' : ''}
      `}
      onClick={handleSelect}
    >
      <div className="flex items-center gap-3">
        {/* 股票代码 */}
        <div className="flex flex-col">
          <span className={`text-sm font-medium ${
            isSelected ? 'text-primary-700' : 'text-gray-900'
          }`}>
            {stock.name}
          </span>
          <span className="text-xs text-gray-500 font-mono">
            {stock.code}
          </span>
        </div>
        
        {/* 选中指示器 */}
        {isSelected && (
          <div className="w-2 h-2 bg-primary-500 rounded-full"></div>
        )}
      </div>

      {/* 操作按钮组 */}
      <div className="flex items-center gap-1">
        {/* 查看资金流向按钮 */}
        <button
          onClick={handleViewFlow}
          className="p-1 text-gray-400 hover:text-blue-600 transition-colors duration-200"
          title="查看资金流向"
        >
          <ExternalLink className="w-4 h-4" />
        </button>

        {/* 删除按钮 */}
        <button
          onClick={handleRemove}
          className="p-1 text-gray-400 hover:text-danger-600 transition-colors duration-200"
          title="删除股票"
        >
          <Trash2 className="w-4 h-4" />
        </button>
      </div>
    </div>
  );
}
