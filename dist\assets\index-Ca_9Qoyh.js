import{r as e,b as t,R as s}from"./vendor-CykFposD.js";import{Q as a,u as r,a as l,b as n,c as i}from"./query-8LrST8fK.js";import{E as c}from"./charts-Bc24t7GR.js";import{A as o,P as d,T as m,a as x,E as h,b as u,S as g,C as f,X as p,B as y,M as b,c as j,d as N,D as v,e as w,f as S,R as k,g as C,h as I,i as L,j as T,k as E,l as A,m as $,n as M,o as R,W as D,p as O,q as P,r as _,L as F}from"./ui-BoieiAXu.js";!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver(e=>{for(const s of e)if("childList"===s.type)for(const e of s.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)}).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();var z={exports:{}},V={},B=e,K=Symbol.for("react.element"),W=Symbol.for("react.fragment"),U=Object.prototype.hasOwnProperty,H=B.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Q={key:!0,ref:!0,__self:!0,__source:!0};function q(e,t,s){var a,r={},l=null,n=null;for(a in void 0!==s&&(l=""+s),void 0!==t.key&&(l=""+t.key),void 0!==t.ref&&(n=t.ref),t)U.call(t,a)&&!Q.hasOwnProperty(a)&&(r[a]=t[a]);if(e&&e.defaultProps)for(a in t=e.defaultProps)void 0===r[a]&&(r[a]=t[a]);return{$$typeof:K,type:e,key:l,ref:n,props:r,_owner:H.current}}V.Fragment=W,V.jsx=q,V.jsxs=q,z.exports=V;var J=z.exports,G={},Z=t;G.createRoot=Z.createRoot,G.hydrateRoot=Z.hydrateRoot;const X=new a({defaultOptions:{queries:{staleTime:3e5,cacheTime:6e5,retry:(e,t)=>{if(t instanceof Error&&"status"in t){const e=t.status;if(e>=400&&e<500)return!1}return e<2},retryDelay:e=>Math.min(1e3*2**e,3e4),refetchOnWindowFocus:!1,refetchOnReconnect:!0,refetchOnMount:!0},mutations:{retry:1,retryDelay:1e3}}}),Y={STOCKS:["stocks"],STOCK_LIST:["stocks","list"],STOCK_DATA:e=>["stockData",e],STOCK_DATA_BATCH:e=>["stockData","batch",e.sort().join(",")],STOCK_LAST_UPDATE:e=>["stockData",e,"lastUpdate"],API_STATUS:["api","status"]},ee={STOCK_LIST:{staleTime:18e5,cacheTime:36e5},STOCK_DATA:{staleTime:6e4,cacheTime:3e5,refetchInterval:6e4,refetchIntervalInBackground:!1},STOCK_DATA_BATCH:{staleTime:6e4,cacheTime:3e5,refetchInterval:6e4},API_STATUS:{staleTime:3e4,cacheTime:12e4,refetchInterval:3e4}};function te(e){const t=e.trim();if(!t)return{isValid:!1,message:"股票代码不能为空"};if(6!==t.length)return{isValid:!1,message:"股票代码必须为6位数字"};if(!/^\d{6}$/.test(t))return{isValid:!1,message:"股票代码只能包含数字"};const s=parseInt(t);return s>=6e5&&s<=699999||s>=0&&s<=99999||s>=2e3&&s<=4999||s>=3e5&&s<=399999||s>=688e3&&s<=689999||s>=43e4&&s<=899999?{isValid:!0}:{isValid:!1,message:"请输入有效的股票代码"}}function se(e){return e.trim().padStart(6,"0")}const ae={"000001":"平安银行","000002":"万科A","000858":"五粮液",600036:"招商银行",600121:"郑州煤电",600519:"贵州茅台","002415":"海康威视",600276:"恒瑞医药","000725":"京东方A",600793:"宜宾纸业",603067:"振华股份"};const re="gupiao-stock-list";function le(){const[t,s]=e.useState([]),[a,r]=e.useState(!1),[l,n]=e.useState(null);e.useEffect(()=>{try{const e=localStorage.getItem(re);if(e){const t=JSON.parse(e);s(t)}}catch(e){n("加载股票列表失败")}},[]);const i=e.useCallback(e=>{try{localStorage.setItem(re,JSON.stringify(e))}catch(t){n("保存股票列表失败")}},[]),c=e.useCallback(async(e,a)=>{r(!0),n(null);try{const r=te(e);if(!r.isValid)return{success:!1,message:r.message};const l=se(e);if(function(e,t){const s=se(e);return t.includes(s)}(l,t.map(e=>e.code)))return{success:!1,message:"股票代码已存在"};const n=a||function(e){return ae[e]||`股票${e}`}(l),c={code:l,name:n},o=[...t,c];return s(o),i(o),{success:!0,message:"股票添加成功"}}catch(l){const e="添加股票失败";return n(e),{success:!1,message:e}}finally{r(!1)}},[t,i]),o=e.useCallback(e=>{n(null);try{const a=t.filter(t=>t.code!==e);s(a),i(a)}catch(a){n("删除股票失败")}},[t,i]),d=e.useCallback(()=>{n(null);try{s([]),i([])}catch(e){n("清空股票列表失败")}},[i]);return{stocks:t,addStock:c,removeStock:o,clearAllStocks:d,isLoading:a,error:l}}const ne=(e,t,s)=>{const a=new Error(e);return a.status=t,a.details=s,a},ie=async(e,t={})=>{const s=`https://gupiao-zijinliu-api-prod.yiukaitlina.workers.dev${e}`,a={headers:{"Content-Type":"application/json",Accept:"application/json"}};try{const e=await fetch(s,{...a,...t});if(!e.ok){const t=await e.text().catch(()=>"Unknown error");throw ne(`HTTP ${e.status}: ${e.statusText}`,e.status,t)}const r=await e.json();if("object"!=typeof r||!("success"in r))throw ne("Invalid API response format");return r}catch(r){if(r instanceof TypeError&&r.message.includes("fetch"))throw ne("Network error: Unable to connect to server");throw r}},ce={async getStocks(){const e=await ie("/api/stocks");if(!e.success)throw ne(e.message||"Failed to get stocks");return e.data||[]},async addStock(e,t){const s=await ie("/api/stocks",{method:"POST",body:JSON.stringify({code:e,name:t})});if(!s.success)throw ne(s.message||"Failed to add stock");return s.data},async removeStock(e){const t=await ie(`/api/stocks/${e}`,{method:"DELETE"});if(!t.success)throw ne(t.message||"Failed to remove stock");return t.data},async addStocksBatch(e){const t=await ie("/api/stocks/batch",{method:"POST",body:JSON.stringify({stocks:e})});if(!t.success)throw ne(t.message||"Failed to add stocks");return t.data},async clearAllStocks(){const e=await ie("/api/stocks",{method:"DELETE"});if(!e.success)throw ne(e.message||"Failed to clear stocks");return e.data}},oe={async getStockData(e,t=240,s=!0){const a=new URLSearchParams({limit:t.toString(),cache:s.toString()}),r=await ie(`/api/data/${e}?${a}`);if(!r.success)throw ne(r.message||"Failed to get stock data");return r.data},async getBatchStockData(e,t=240,s=!0){const a=new URLSearchParams({codes:e.join(","),limit:t.toString(),cache:s.toString()}),r=await ie(`/api/data/batch?${a}`);if(!r.success)throw ne(r.message||"Failed to get batch stock data");return r.data},async getLastUpdate(e){const t=await ie(`/api/data/${e}/last-update`);if(!t.success)throw ne(t.message||"Failed to get last update");return t.data},async clearCache(e){const t=await ie(`/api/data/${e}/cache`,{method:"DELETE"});if(!t.success)throw ne(t.message||"Failed to clear cache");return t.data},async getServiceStatus(){const e=await ie("/api/data/status");if(!e.success)throw ne(e.message||"Failed to get service status");return e.data}};function de({onAddStock:t,isLoading:s=!1}){const[a,r]=e.useState(""),[l,n]=e.useState(null),[i,c]=e.useState(!1),m=e.useCallback(function(e,t){let s;return(...a)=>{clearTimeout(s),s=window.setTimeout(()=>e(...a),t)}}(e=>{if(!e.trim())return n(null),void c(!1);const t=te(e);n(t.isValid?null:t.message||"无效的股票代码"),c(!1)},300),[]),x=async()=>{var e;if(a.trim()&&!l&&!s)try{const s=(null==(e=(await oe.getStockData(a,1)).summary)?void 0:e.name)||`股票${a}`,l=await t(a,s);l.success?(r(""),n(null)):n(l.message||"添加失败")}catch(i){(await t(a)).success?(r(""),n(null)):n("股票代码无效或获取信息失败")}},h=null!==l,u=a.trim()&&!h&&!i&&!s;return J.jsxs("div",{className:"space-y-3",children:[J.jsxs("div",{className:"flex gap-2",children:[J.jsxs("div",{className:"flex-1",children:[J.jsx("input",{type:"text",value:a,onChange:e=>{const t=e.target.value.replace(/\D/g,"").slice(0,6);r(t),t.trim()?(c(!0),m(t)):(n(null),c(!1))},onKeyPress:e=>{"Enter"===e.key&&x()},placeholder:"输入6位股票代码",className:"input "+(h?"border-danger-500 focus:ring-danger-500":""),disabled:s,maxLength:6}),i&&J.jsx("div",{className:"mt-1 text-sm text-gray-500",children:"验证中..."}),h&&J.jsxs("div",{className:"mt-1 flex items-center gap-1 text-sm text-danger-600",children:[J.jsx(o,{className:"w-4 h-4"}),J.jsx("span",{children:l})]})]}),J.jsxs("button",{onClick:x,disabled:!u,className:`btn ${u?"btn-primary":"btn-secondary"} flex items-center gap-2 px-4 py-2`,children:[s?J.jsx("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}):J.jsx(d,{className:"w-4 h-4"}),"添加"]})]}),J.jsx("div",{className:"text-xs text-gray-500",children:"支持的股票代码：沪市(600xxx)、深市(000xxx)、创业板(300xxx)、科创板(688xxx)等"})]})}function me({stocks:e,onRemoveStock:t,onSelectStock:s,selectedStock:a}){return 0===e.length?J.jsxs("div",{className:"text-center py-8",children:[J.jsx(m,{className:"w-12 h-12 text-gray-300 mx-auto mb-3"}),J.jsx("p",{className:"text-gray-500 mb-2",children:"暂无股票代码"}),J.jsx("p",{className:"text-sm text-gray-400",children:"请添加股票代码开始监控"})]}):J.jsxs("div",{className:"space-y-2",children:[J.jsxs("div",{className:"flex items-center justify-between text-sm text-gray-600 pb-2 border-b border-gray-200",children:[J.jsxs("span",{children:["股票代码 (",e.length,")"]}),J.jsx("span",{children:"操作"})]}),J.jsx("div",{className:"space-y-1 max-h-64 overflow-y-auto",children:e.map(e=>J.jsx(xe,{stock:e,isSelected:a===e.code,onSelect:s,onRemove:t},e.code))}),e.length>1&&J.jsx("div",{className:"pt-3 border-t border-gray-200",children:J.jsxs("button",{onClick:()=>{window.confirm("确定要清空所有股票代码吗？")&&e.forEach(e=>t(e.code))},className:"text-sm text-danger-600 hover:text-danger-700 flex items-center gap-1",children:[J.jsx(x,{className:"w-4 h-4"}),"清空所有"]})})]})}function xe({stock:e,isSelected:t,onSelect:s,onRemove:a}){return J.jsxs("div",{className:`\n        flex items-center justify-between p-3 rounded-lg border transition-all duration-200\n        ${t?"border-primary-500 bg-primary-50":"border-gray-200 hover:border-gray-300 hover:bg-gray-50"}\n        ${s?"cursor-pointer":""}\n      `,onClick:()=>{s&&s(e.code)},children:[J.jsxs("div",{className:"flex items-center gap-3",children:[J.jsxs("div",{className:"flex flex-col",children:[J.jsx("span",{className:"text-sm font-medium "+(t?"text-primary-700":"text-gray-900"),children:e.name}),J.jsx("span",{className:"text-xs text-gray-500 font-mono",children:e.code})]}),t&&J.jsx("div",{className:"w-2 h-2 bg-primary-500 rounded-full"})]}),J.jsxs("div",{className:"flex items-center gap-1",children:[J.jsx("button",{onClick:t=>{t.stopPropagation();const s=`https://data.eastmoney.com/zjlx/${e.code}.html`;window.open(s,"_blank")},className:"p-1 text-gray-400 hover:text-blue-600 transition-colors duration-200",title:"查看资金流向",children:J.jsx(h,{className:"w-4 h-4"})}),J.jsx("button",{onClick:t=>{t.stopPropagation(),window.confirm(`确定要删除股票 ${e.code} 吗？`)&&a(e.code)},className:"p-1 text-gray-400 hover:text-danger-600 transition-colors duration-200",title:"删除股票",children:J.jsx(u,{className:"w-4 h-4"})})]})]})}function he({onSelectStock:t,selectedStock:s}){const{stocks:a,addStock:r,removeStock:l,isLoading:n,error:i}=le(),[c,o]=e.useState(null),d=(e,t)=>{o({type:e,message:t}),setTimeout(()=>o(null),3e3)};return J.jsxs("div",{className:"card p-6 h-full",children:[J.jsxs("div",{className:"flex items-center justify-between mb-6",children:[J.jsxs("div",{children:[J.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"股票管理"}),J.jsx("p",{className:"text-sm text-gray-600 mt-1",children:"添加和管理要监控的股票代码"})]}),J.jsx("button",{className:"p-2 text-gray-400 hover:text-gray-600 transition-colors",children:J.jsx(g,{className:"w-5 h-5"})})]}),c&&J.jsxs("div",{className:`\n          flex items-center gap-2 p-3 rounded-lg mb-4 animate-slide-up\n          ${"success"===c.type?"bg-success-50 text-success-700 border border-success-200":"bg-danger-50 text-danger-700 border border-danger-200"}\n        `,children:["success"===c.type?J.jsx(f,{className:"w-4 h-4"}):J.jsx(p,{className:"w-4 h-4"}),J.jsx("span",{className:"text-sm",children:c.message})]}),i&&J.jsxs("div",{className:"flex items-center gap-2 p-3 rounded-lg mb-4 bg-danger-50 text-danger-700 border border-danger-200",children:[J.jsx(p,{className:"w-4 h-4"}),J.jsx("span",{className:"text-sm",children:i})]}),J.jsx("div",{className:"mb-6",children:J.jsx(de,{onAddStock:async(e,t)=>{const s=await r(e,t);return s.success?d("success",s.message||"股票添加成功"):d("error",s.message||"添加失败"),s},isLoading:n})}),J.jsx("div",{className:"flex-1",children:J.jsx(me,{stocks:a,onRemoveStock:e=>{l(e),d("success","股票删除成功"),s===e&&t&&t("")},onSelectStock:t,selectedStock:s})}),a.length>0&&J.jsx("div",{className:"mt-6 pt-4 border-t border-gray-200",children:J.jsxs("div",{className:"flex items-center justify-between text-sm text-gray-600",children:[J.jsxs("span",{children:["已添加 ",a.length," 只股票"]}),J.jsx("span",{children:s?`当前选中: ${s}`:"请选择股票查看数据"})]})})]})}function ue(e,t="wan",s=2){if(0===e)return"0";let a=1,r="";switch(t){case"yuan":a=1,r="元";break;case"wan":a=1e4,r="万";break;case"yi":a=1e8,r="亿"}return`${(e/a).toFixed(s)}${r}`}function ge(e,t=2){const s=Math.abs(e);return s>=1e8?ue(e,"yi",t):s>=1e4?ue(e,"wan",t):ue(e,"yuan",0)}function fe(e,t="time"){const s=new Date(e);if(isNaN(s.getTime()))return e;switch(t){case"time":return s.toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit"});case"date":return s.toLocaleDateString("zh-CN");case"datetime":return s.toLocaleString("zh-CN");default:return e}}function pe(e){return e>0?"#ef4444":e<0?"#22c55e":"#6b7280"}function ye(e,t=2){return e.toLocaleString("zh-CN",{minimumFractionDigits:t,maximumFractionDigits:t})}function be(e){const t=new Date,s=new Date(e),a=t.getTime()-s.getTime(),r=Math.floor(a/6e4);if(r<1)return"刚刚";if(r<60)return`${r}分钟前`;if(r<1440){return`${Math.floor(r/60)}小时前`}return`${Math.floor(r/1440)}天前`}const je="#FF6B9D",Ne="#C44569",ve="#F8B500",we="#6C7CE0",Se="#A0E7E5",ke={backgroundColor:"#ffffff",textColor:"#333333",axisLineColor:"#e5e7eb",splitLineColor:"#f3f4f6",tooltipBackgroundColor:"rgba(255, 255, 255, 0.95)",tooltipBorderColor:"#e5e7eb"},Ce={backgroundColor:"#1f2937",textColor:"#f9fafb",axisLineColor:"#374151",splitLineColor:"#4b5563",tooltipBackgroundColor:"rgba(31, 41, 55, 0.95)",tooltipBorderColor:"#6b7280"};function Ie(e=!1){const t=e?Ce:ke;return{backgroundColor:t.backgroundColor,textStyle:{color:t.textColor,fontFamily:"system-ui, -apple-system, sans-serif"},grid:{left:"8%",right:"5%",bottom:"15%",top:"15%",containLabel:!0},tooltip:{backgroundColor:t.tooltipBackgroundColor,borderColor:t.tooltipBorderColor,borderWidth:1,textStyle:{color:t.textColor},trigger:"axis",axisPointer:{type:"cross",crossStyle:{color:"#999"}}},legend:{textStyle:{color:t.textColor}}}}function Le(e){const t=e<768;return{grid:{left:t?"5%":"3%",right:t?"5%":"4%",bottom:t?"15%":"3%",containLabel:!0},legend:{orient:"horizontal",top:t?"bottom":30,itemWidth:t?15:25,itemHeight:t?10:14,textStyle:{fontSize:t?10:12}},xAxis:{axisLabel:{fontSize:t?10:12,rotate:t?45:0}},yAxis:{axisLabel:{fontSize:t?10:12}},dataZoom:t?[]:[{type:"inside",start:70,end:100},{type:"slider",start:70,end:100,height:20,bottom:10}]}}const Te=({klines:t=[],summary:s=null,type:a="line",isDark:r=!1,height:l=400,showToolbar:n=!0,loading:i=!1,error:o=null,className:d=""})=>{const x=e.useRef(null),h=e.useRef(null),[u,g]=e.useState(a),[f,p]=e.useState(!1),[N,v]=e.useState(800);e.useEffect(()=>{const e=()=>{h.current&&v(h.current.offsetWidth)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]);const w=e.useMemo(()=>{if("line"===u&&t.length>0){t.slice(0,5),Math.min(...t.map(e=>e.mainNetInflow)),Math.max(...t.map(e=>e.mainNetInflow)),Math.min(...t.map(e=>e.superLargeNetInflow)),Math.max(...t.map(e=>e.superLargeNetInflow)),Math.min(...t.map(e=>e.largeNetInflow)),Math.max(...t.map(e=>e.largeNetInflow)),Math.min(...t.map(e=>e.mediumNetInflow)),Math.max(...t.map(e=>e.mediumNetInflow)),Math.min(...t.map(e=>e.smallNetInflow)),Math.max(...t.map(e=>e.smallNetInflow)),t.slice(0,10).map(e=>({time:e.time,main:e.mainNetInflow,superLarge:e.superLargeNetInflow,large:e.largeNetInflow,medium:e.mediumNetInflow,small:e.smallNetInflow}));const e=function(e,t=!1){const s=Ie(t),a=t?Ce:ke,r=e.map(e=>fe(e.time)),l=e.flatMap(e=>[e.mainNetInflow,e.superLargeNetInflow,e.largeNetInflow,e.mediumNetInflow,e.smallNetInflow]),n=Math.min(...l),i=Math.max(...l),c=i-n>.8*Math.abs(i),o=[{name:"主力净流入",type:"line",data:e.map(e=>e.mainNetInflow),color:je,smooth:!0,symbol:"circle",symbolSize:6,showSymbol:!0,lineStyle:{width:4,type:"solid"},emphasis:{lineStyle:{width:5},focus:"series"},areaStyle:{opacity:.15}},{name:"超大单净流入",type:"line",data:e.map(e=>e.superLargeNetInflow),color:Ne,smooth:!0,symbol:"diamond",symbolSize:5,showSymbol:!0,lineStyle:{width:3,type:"dashed"},emphasis:{lineStyle:{width:4},focus:"series"},areaStyle:{opacity:.12}},{name:"大单净流入",type:"line",data:e.map(e=>e.largeNetInflow),color:ve,smooth:!0,symbol:"triangle",symbolSize:5,showSymbol:!0,lineStyle:{width:3,type:"dotted"},emphasis:{lineStyle:{width:4},focus:"series"},areaStyle:{opacity:.1}},{name:"中单净流入",type:"line",data:e.map(e=>e.mediumNetInflow),color:we,smooth:!0,symbol:"rect",symbolSize:4,showSymbol:!0,lineStyle:{width:2.5,type:"solid"},emphasis:{lineStyle:{width:3.5},focus:"series"},areaStyle:{opacity:.08}},{name:"小单净流入",type:"line",data:e.map(e=>e.smallNetInflow),color:Se,smooth:!0,symbol:"pin",symbolSize:4,showSymbol:!0,lineStyle:{width:2,type:"solid"},emphasis:{lineStyle:{width:3},focus:"series"},areaStyle:{opacity:.06}}];return{...s,title:{text:"资金流向趋势图",textStyle:{color:a.textColor,fontSize:16,fontWeight:"bold"},left:"center"},tooltip:{...s.tooltip,trigger:"axis",axisPointer:{type:"cross",label:{backgroundColor:a.textColor}},formatter:e=>{var t;if(!Array.isArray(e))return"";let s=`<div style="margin-bottom: 4px; font-weight: bold;">${(null==(t=e[0])?void 0:t.axisValue)||""}</div>`;return e.forEach(e=>{const t=ge(e.value),a=e.color;s+=`\n            <div style="display: flex; align-items: center; margin: 2px 0;">\n              <span style="display: inline-block; width: 10px; height: 10px; background-color: ${a}; border-radius: 50%; margin-right: 8px;"></span>\n              <span style="flex: 1;">${e.seriesName}:</span>\n              <span style="font-weight: bold; color: ${pe(e.value)};">${t}</span>\n            </div>\n          `}),s}},legend:{...s.legend,top:30,data:o.map(e=>e.name),orient:"horizontal",left:"center",itemWidth:25,itemHeight:14,itemGap:20,textStyle:{fontSize:12,color:a.textColor},icon:"line",lineStyle:{width:3}},xAxis:{type:"category",data:r,axisLine:{lineStyle:{color:a.axisLineColor}},axisLabel:{color:a.textColor,rotate:45},splitLine:{show:!1}},yAxis:{type:"value",axisLine:{lineStyle:{color:a.axisLineColor}},axisLabel:{color:a.textColor,formatter:e=>ge(e)},splitLine:{lineStyle:{color:a.splitLineColor}},scale:!0,min:function(e){return c?.95*e.min:e.min},max:function(e){return c?1.05*e.max:e.max}},series:o,dataZoom:[{type:"inside",start:70,end:100},{type:"slider",start:70,end:100,height:20,bottom:10}]}}(t,r);return{...e,...Le(N)}}if("bar"===u&&s){const e=function(e,t=!1){const s=Ie(t),a=t?Ce:ke,r=[e.mainNetInflow,e.superLargeNetInflow,e.largeNetInflow,e.mediumNetInflow,e.smallNetInflow],l=[je,Ne,ve,we,Se];return{...s,title:{text:"实时资金流向",textStyle:{color:a.textColor,fontSize:16,fontWeight:"bold"},left:"center"},tooltip:{...s.tooltip,trigger:"axis",formatter:e=>{if(!Array.isArray(e)||0===e.length)return"";const t=e[0],s=ge(t.value),a=t.value>=0?"流入":"流出",r=pe(t.value);return`\n          <div style="font-weight: bold; margin-bottom: 4px;">${t.name}净${a}</div>\n          <div style="color: ${r}; font-size: 14px; font-weight: bold;">${s}</div>\n        `}},xAxis:{type:"category",data:["主力","超大单","大单","中单","小单"],axisLine:{lineStyle:{color:a.axisLineColor}},axisLabel:{color:a.textColor}},yAxis:{type:"value",axisLine:{lineStyle:{color:a.axisLineColor}},axisLabel:{color:a.textColor,formatter:e=>ge(e)},splitLine:{lineStyle:{color:a.splitLineColor}}},series:[{type:"bar",data:r.map((e,t)=>({value:e,itemStyle:{color:l[t]}})),barWidth:"60%",label:{show:!0,position:"top",formatter:e=>ge(e.value),color:a.textColor}}]}}(s,r);return{...e,...Le(N)}}return{}},[u,t,s,r,N]),S=e=>{g(e)};return i?J.jsx("div",{className:`flex items-center justify-center bg-white rounded-lg border ${d}`,style:{height:l},children:J.jsxs("div",{className:"text-center",children:[J.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto mb-2"}),J.jsx("p",{className:"text-gray-600",children:"加载图表数据中..."})]})}):o?J.jsx("div",{className:`flex items-center justify-center bg-white rounded-lg border ${d}`,style:{height:l},children:J.jsxs("div",{className:"text-center",children:[J.jsx("div",{className:"text-red-500 mb-2",children:J.jsx(y,{className:"w-8 h-8 mx-auto"})}),J.jsx("p",{className:"text-red-600 font-medium",children:"图表加载失败"}),J.jsx("p",{className:"text-gray-500 text-sm mt-1",children:o})]})}):0!==t.length||s?J.jsxs("div",{ref:h,className:`bg-white rounded-lg border ${f?"fixed inset-0 z-50":""} ${d}`,children:[n&&J.jsxs("div",{className:"flex items-center justify-between p-4 border-b",children:[J.jsxs("div",{className:"flex items-center gap-2",children:[J.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"资金流向图表"}),s&&J.jsxs("span",{className:"text-sm text-gray-500",children:[s.name," (",s.code,")"]})]}),J.jsxs("div",{className:"flex items-center gap-2",children:[J.jsxs("div",{className:"flex bg-gray-100 rounded-lg p-1",children:[J.jsxs("button",{onClick:()=>S("line"),className:"px-3 py-1 rounded text-sm transition-colors "+("line"===u?"bg-white text-primary-600 shadow-sm":"text-gray-600 hover:text-gray-900"),disabled:0===t.length,children:[J.jsx(m,{className:"w-4 h-4 inline mr-1"}),"趋势图"]}),J.jsxs("button",{onClick:()=>S("bar"),className:"px-3 py-1 rounded text-sm transition-colors "+("bar"===u?"bg-white text-primary-600 shadow-sm":"text-gray-600 hover:text-gray-900"),disabled:!s,children:[J.jsx(y,{className:"w-4 h-4 inline mr-1"}),"柱状图"]})]}),J.jsx("button",{onClick:()=>{if(x.current){x.current.getEchartsInstance().resize()}},className:"p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded",title:"刷新图表",children:J.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:J.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})})}),J.jsx("button",{onClick:()=>{if(x.current){const e=x.current.getEchartsInstance().getDataURL({type:"png",backgroundColor:r?"#1f2937":"#ffffff"}),t=document.createElement("a");t.download=`资金流向图表_${(new Date).toISOString().slice(0,10)}.png`,t.href=e,t.click()}},className:"p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded",title:"导出图表",children:J.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:J.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})}),J.jsx("button",{onClick:()=>{p(!f)},className:"p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded",title:f?"退出全屏":"全屏显示",children:f?J.jsx(b,{className:"w-4 h-4"}):J.jsx(j,{className:"w-4 h-4"})})]})]}),J.jsx("div",{className:"p-4",children:J.jsx(c,{ref:x,option:w,style:{height:f?"calc(100vh - 120px)":l,width:"100%"},notMerge:!0,lazyUpdate:!0,theme:r?"dark":void 0})})]}):J.jsx("div",{className:`flex items-center justify-center bg-white rounded-lg border ${d}`,style:{height:l},children:J.jsxs("div",{className:"text-center",children:[J.jsx("div",{className:"text-gray-400 mb-2",children:J.jsx(y,{className:"w-8 h-8 mx-auto"})}),J.jsx("p",{className:"text-gray-600",children:"暂无图表数据"}),J.jsx("p",{className:"text-gray-500 text-sm mt-1",children:"请选择股票查看资金流向"})]})})},Ee=({klines:t=[],summary:s=null,pageSize:a=20,showSearch:r=!0,showExport:l=!0,className:n=""})=>{const[i,c]=e.useState(1),[o,d]=e.useState("time"),[m,x]=e.useState("desc"),[h,u]=e.useState(""),g=[{key:"time",label:"时间",sortable:!0,width:"120px"},{key:"mainNetInflow",label:"主力净流入",sortable:!0,width:"120px"},{key:"superLargeNetInflow",label:"超大单净流入",sortable:!0,width:"130px"},{key:"largeNetInflow",label:"大单净流入",sortable:!0,width:"120px"},{key:"mediumNetInflow",label:"中单净流入",sortable:!0,width:"120px"},{key:"smallNetInflow",label:"小单净流入",sortable:!0,width:"120px"}],f=e.useMemo(()=>{let e=t;return h&&(e=t.filter(e=>fe(e.time,"datetime").includes(h))),e.sort((e,t)=>{let s=e[o],a=t[o];return"time"===o&&(s=new Date(s).getTime(),a=new Date(a).getTime()),"asc"===m?s>a?1:-1:s<a?1:-1}),e},[t,h,o,m]),p=e.useMemo(()=>{const e=(i-1)*a,t=e+a;return f.slice(e,t)},[f,i,a]),y=Math.ceil(f.length/a),b=e=>{const t=ge(e),s=pe(e),a=function(e){return e>0?"流入":e<0?"流出":"平"}(e);return J.jsx("span",{style:{color:s},className:"font-medium",children:"平"===a?t:`${a} ${t}`})};return 0===t.length?J.jsxs("div",{className:`bg-white rounded-lg border p-8 text-center ${n}`,children:[J.jsx("div",{className:"text-gray-400 mb-2",children:J.jsx("svg",{className:"w-12 h-12 mx-auto",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:J.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 17v-2m3 2v-4m3 4v-6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})}),J.jsx("p",{className:"text-gray-600",children:"暂无数据"}),J.jsx("p",{className:"text-gray-500 text-sm mt-1",children:"请选择股票查看详细数据"})]}):J.jsxs("div",{className:`bg-white rounded-lg border ${n}`,children:[J.jsx("div",{className:"p-4 border-b",children:J.jsxs("div",{className:"flex items-center justify-between",children:[J.jsxs("div",{children:[J.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"资金流向明细"}),s&&J.jsxs("p",{className:"text-sm text-gray-500 mt-1",children:[s.name," (",s.code,") - 共 ",f.length," 条记录"]})]}),J.jsxs("div",{className:"flex items-center gap-2",children:[r&&J.jsxs("div",{className:"relative",children:[J.jsx(N,{className:"w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),J.jsx("input",{type:"text",placeholder:"搜索时间...",value:h,onChange:e=>u(e.target.value),className:"pl-10 pr-4 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-primary-500 focus:border-primary-500"})]}),l&&J.jsxs("button",{onClick:()=>{const e=[g.map(e=>e.label).join(","),...f.map(e=>g.map(t=>"time"===t.key?fe(e[t.key],"datetime"):ge(e[t.key])).join(","))].join("\n"),t=new Blob(["\ufeff"+e],{type:"text/csv;charset=utf-8;"}),s=document.createElement("a");s.href=URL.createObjectURL(t),s.download=`资金流向数据_${(new Date).toISOString().slice(0,10)}.csv`,s.click()},className:"flex items-center gap-2 px-3 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors text-sm",children:[J.jsx(v,{className:"w-4 h-4"}),"导出CSV"]})]})]})}),J.jsx("div",{className:"overflow-x-auto",children:J.jsxs("table",{className:"w-full",children:[J.jsx("thead",{className:"bg-gray-50",children:J.jsx("tr",{children:g.map(e=>{return J.jsx("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider "+(e.sortable?"cursor-pointer hover:bg-gray-100":""),style:{width:e.width},onClick:()=>{return e.sortable&&(t=e.key,o===t?x("asc"===m?"desc":"asc"):(d(t),x("desc")),void c(1));var t},children:J.jsxs("div",{className:"flex items-center gap-1",children:[e.label,e.sortable&&(t=e.key,o!==t?J.jsx(w,{className:"w-4 h-4 text-gray-400"}):"asc"===m?J.jsx(w,{className:"w-4 h-4 text-primary-600"}):J.jsx(S,{className:"w-4 h-4 text-primary-600"}))]})},e.key);var t})})}),J.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:p.map((e,t)=>J.jsxs("tr",{className:"hover:bg-gray-50",children:[J.jsx("td",{className:"px-4 py-3 text-sm text-gray-900",children:fe(e.time,"datetime")}),J.jsx("td",{className:"px-4 py-3 text-sm",children:b(e.mainNetInflow)}),J.jsx("td",{className:"px-4 py-3 text-sm",children:b(e.superLargeNetInflow)}),J.jsx("td",{className:"px-4 py-3 text-sm",children:b(e.largeNetInflow)}),J.jsx("td",{className:"px-4 py-3 text-sm",children:b(e.mediumNetInflow)}),J.jsx("td",{className:"px-4 py-3 text-sm",children:b(e.smallNetInflow)})]},`${e.time}-${t}`))})]})}),y>1&&J.jsxs("div",{className:"px-4 py-3 border-t bg-gray-50 flex items-center justify-between",children:[J.jsxs("div",{className:"text-sm text-gray-700",children:["显示第 ",(i-1)*a+1," - ",Math.min(i*a,f.length)," 条， 共 ",f.length," 条记录"]}),J.jsxs("div",{className:"flex items-center gap-2",children:[J.jsx("button",{onClick:()=>c(Math.max(1,i-1)),disabled:1===i,className:"px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-100",children:"上一页"}),J.jsxs("span",{className:"text-sm text-gray-700",children:["第 ",i," / ",y," 页"]}),J.jsx("button",{onClick:()=>c(Math.min(y,i+1)),disabled:i===y,className:"px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-100",children:"下一页"})]})]})]})};function Ae(){const e=r(),t=l(Y.STOCK_LIST,ce.getStocks,ee.STOCK_LIST),s=n(({code:e,name:t})=>ce.addStock(e,t),{onSuccess:()=>{e.invalidateQueries(Y.STOCK_LIST)}}),a=n(e=>ce.removeStock(e),{onSuccess:(t,s)=>{e.invalidateQueries(Y.STOCK_LIST),(e=>{X.removeQueries(Y.STOCK_DATA(e)),X.removeQueries(Y.STOCK_LAST_UPDATE(e))})(s)}}),i=n(e=>ce.addStocksBatch(e),{onSuccess:()=>{e.invalidateQueries(Y.STOCK_LIST)}}),c=n(ce.clearAllStocks,{onSuccess:()=>{e.invalidateQueries(Y.STOCK_LIST),e.clear()}}),o=t.data||[];return{...t,data:t.data,stocks:o,isLoading:t.isLoading||s.isLoading||a.isLoading,isError:t.isError,isSuccess:t.isSuccess,isFetching:t.isFetching,isRefetching:t.isRefetching,error:t.error,refetch:t.refetch,remove:t.remove,addStock:async(e,t)=>{await s.mutateAsync({code:e,name:t})},removeStock:async e=>{await a.mutateAsync(e)},addStocksBatch:async e=>{await i.mutateAsync(e)},clearAllStocks:async()=>{await c.mutateAsync()}}}const $e={minDataPoints:8,minDropAmount:5e5,minReboundAmount:3e5,minDropRatio:.1,minReboundRatio:.15,analysisWindow:12};function Me(e,t={}){const s={...$e,...t},a={hasVPattern:!1,confidence:0,dropAmount:0,reboundAmount:0,dropRatio:0,reboundRatio:0,bottomIndex:-1,analysisData:[]};if(e.length<s.minDataPoints)return a;const r=e.slice(-s.analysisWindow);if(a.analysisData=r,r.length<s.minDataPoints)return a;const l=r.map(e=>e.mainNetInflow),n=Math.min(...l),i=l.indexOf(n);if(a.bottomIndex=i,i<=1||i>=l.length-2)return a;const c=l[0],o=l[i],d=c-o,m=0!==c?Math.abs(d/c):0;a.dropAmount=d,a.dropRatio=m;const x=l[l.length-1]-o,h=0!==o?Math.abs(x/o):0;a.reboundAmount=x,a.reboundRatio=h;const u=d>=s.minDropAmount,g=x>=s.minReboundAmount,f=m>=s.minDropRatio,p=h>=s.minReboundRatio,y=(u||f)&&(g||p)&&d>0&&x>0;if(a.hasVPattern=y,y){let e=0;e=.4*Math.min(m/s.minDropRatio,1)+.4*Math.min(h/s.minReboundRatio,1)+.2*Math.min(x/d,d/x),a.confidence=Math.min(e,1)}return a}const Re=({klines:t=[],height:s=80,showVPattern:a=!1,loading:r=!1,error:l=null,className:n=""})=>{const i=e.useMemo(()=>!(!a||0===t.length)&&function(e,t={}){return Me(e,{minDropAmount:t.minDrop||$e.minDropAmount,minReboundAmount:t.minRebound||$e.minReboundAmount}).hasVPattern}(t),[t,a]),o=e.useMemo(()=>{if(0===t.length)return{};const e=t.map(e=>e.mainNetInflow),s=Math.min(...e),a=Math.max(...e),r=.1*Math.abs(a-s),l=i?"#ef4444":je,n=i?"rgba(239, 68, 68, 0.1)":"rgba(255, 107, 157, 0.1)";return{grid:{top:2,right:2,bottom:2,left:2,containLabel:!1},xAxis:{type:"category",show:!1,data:t.map((e,t)=>t),boundaryGap:!1},yAxis:{type:"value",show:!1,min:s-r,max:a+r},series:[{type:"line",data:e,smooth:!0,symbol:"none",lineStyle:{width:i?3:2,color:l,type:"solid"},areaStyle:{color:n},animation:!1}],tooltip:{show:!1},legend:{show:!1},animation:!1,silent:!0}},[t,i]);return r?J.jsx("div",{className:`flex items-center justify-center bg-gray-50 rounded ${n}`,style:{height:s},children:J.jsx("div",{className:"w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"})}):l?J.jsx("div",{className:`flex items-center justify-center bg-red-50 text-red-500 text-xs rounded ${n}`,style:{height:s},children:J.jsx("span",{children:"图表加载失败"})}):0===t.length?J.jsx("div",{className:`flex items-center justify-center bg-gray-50 text-gray-400 text-xs rounded ${n}`,style:{height:s},children:J.jsx("span",{children:"暂无数据"})}):J.jsxs("div",{className:`relative ${n}`,style:{height:s},children:[J.jsx(c,{option:o,style:{width:"100%",height:"100%"},opts:{renderer:"canvas",width:"auto",height:"auto"}}),i&&J.jsx("div",{className:"absolute top-1 right-1",children:J.jsx("div",{className:"w-2 h-2 bg-red-500 rounded-full animate-pulse"})})]})},De=({stockCode:t,limit:s=240,className:a=""})=>{const[r,n]=e.useState("chart"),{summary:i,klines:c,isLoading:d,isError:x,error:h,refetch:u,isFetching:g}=function(e,t=240,s={}){const{enabled:a=!0,useCache:r=!0,refetchInterval:n}=s,i=l(Y.STOCK_DATA(e),()=>oe.getStockData(e,t,r),{...ee.STOCK_DATA,enabled:a&&!!e,refetchInterval:n??ee.STOCK_DATA.refetchInterval}),c=i.data,o=(null==c?void 0:c.summary)||null,d=(null==c?void 0:c.klines)||[],m=(null==o?void 0:o.lastUpdate)||null;return{...i,data:i.data,summary:o,klines:d,lastUpdate:m,isLoading:i.isLoading,isError:i.isError,isSuccess:i.isSuccess,isFetching:i.isFetching,isRefetching:i.isRefetching,error:i.error,refetch:i.refetch,remove:i.remove}}(t,s,{enabled:!!t});return d?J.jsx("div",{className:`bg-white rounded-lg border p-8 ${a}`,children:J.jsxs("div",{className:"text-center",children:[J.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"}),J.jsx("p",{className:"text-gray-600 font-medium",children:"正在加载股票数据..."}),J.jsxs("p",{className:"text-gray-500 text-sm mt-1",children:["股票代码: ",t]})]})}):x?J.jsx("div",{className:`bg-white rounded-lg border p-8 ${a}`,children:J.jsxs("div",{className:"text-center",children:[J.jsx("div",{className:"text-red-500 mb-4",children:J.jsx(o,{className:"w-12 h-12 mx-auto"})}),J.jsx("p",{className:"text-red-600 font-medium mb-2",children:"数据加载失败"}),J.jsx("p",{className:"text-gray-500 text-sm mb-4",children:(null==h?void 0:h.message)||"无法获取股票数据，请稍后重试"}),J.jsxs("button",{onClick:()=>u(),className:"btn btn-primary flex items-center gap-2 mx-auto",children:[J.jsx(k,{className:"w-4 h-4"}),"重新加载"]})]})}):i||0!==c.length?J.jsxs("div",{className:`space-y-6 ${a}`,children:[i&&J.jsxs("div",{className:"bg-white rounded-lg border p-6",children:[J.jsxs("div",{className:"flex items-center justify-between mb-4",children:[J.jsxs("div",{children:[J.jsx("h2",{className:"text-xl font-bold text-gray-900",children:i.name}),J.jsxs("p",{className:"text-gray-500",children:[i.code," · 最后更新: ",be(i.lastUpdate)]})]}),g&&J.jsxs("div",{className:"flex items-center gap-2 text-primary-600",children:[J.jsx(k,{className:"w-4 h-4 animate-spin"}),J.jsx("span",{className:"text-sm",children:"更新中..."})]})]}),J.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-5 gap-4",children:[J.jsxs("div",{className:"text-center p-3 bg-gray-50 rounded-lg",children:[J.jsx("div",{className:"text-xs text-gray-500 mb-1",children:"主力净流入"}),J.jsx("div",{className:"text-lg font-bold",style:{color:pe(i.mainNetInflow)},children:ge(i.mainNetInflow)}),J.jsx("div",{className:"flex items-center justify-center mt-1",children:i.mainNetInflow>=0?J.jsx(m,{className:"w-3 h-3 text-red-500"}):J.jsx(C,{className:"w-3 h-3 text-green-500"})})]}),J.jsxs("div",{className:"text-center p-3 bg-gray-50 rounded-lg",children:[J.jsx("div",{className:"text-xs text-gray-500 mb-1",children:"超大单净流入"}),J.jsx("div",{className:"text-lg font-bold",style:{color:pe(i.superLargeNetInflow)},children:ge(i.superLargeNetInflow)}),J.jsx("div",{className:"flex items-center justify-center mt-1",children:i.superLargeNetInflow>=0?J.jsx(m,{className:"w-3 h-3 text-red-500"}):J.jsx(C,{className:"w-3 h-3 text-green-500"})})]}),J.jsxs("div",{className:"text-center p-3 bg-gray-50 rounded-lg",children:[J.jsx("div",{className:"text-xs text-gray-500 mb-1",children:"大单净流入"}),J.jsx("div",{className:"text-lg font-bold",style:{color:pe(i.largeNetInflow)},children:ge(i.largeNetInflow)}),J.jsx("div",{className:"flex items-center justify-center mt-1",children:i.largeNetInflow>=0?J.jsx(m,{className:"w-3 h-3 text-red-500"}):J.jsx(C,{className:"w-3 h-3 text-green-500"})})]}),J.jsxs("div",{className:"text-center p-3 bg-gray-50 rounded-lg",children:[J.jsx("div",{className:"text-xs text-gray-500 mb-1",children:"中单净流入"}),J.jsx("div",{className:"text-lg font-bold",style:{color:pe(i.mediumNetInflow)},children:ge(i.mediumNetInflow)}),J.jsx("div",{className:"flex items-center justify-center mt-1",children:i.mediumNetInflow>=0?J.jsx(m,{className:"w-3 h-3 text-red-500"}):J.jsx(C,{className:"w-3 h-3 text-green-500"})})]}),J.jsxs("div",{className:"text-center p-3 bg-gray-50 rounded-lg",children:[J.jsx("div",{className:"text-xs text-gray-500 mb-1",children:"小单净流入"}),J.jsx("div",{className:"text-lg font-bold",style:{color:pe(i.smallNetInflow)},children:ge(i.smallNetInflow)}),J.jsx("div",{className:"flex items-center justify-center mt-1",children:i.smallNetInflow>=0?J.jsx(m,{className:"w-3 h-3 text-red-500"}):J.jsx(C,{className:"w-3 h-3 text-green-500"})})]})]})]}),J.jsxs("div",{className:"bg-white rounded-lg border",children:[J.jsx("div",{className:"border-b",children:J.jsxs("nav",{className:"flex",children:[J.jsxs("button",{onClick:()=>n("chart"),className:"px-6 py-3 text-sm font-medium border-b-2 transition-colors "+("chart"===r?"border-primary-600 text-primary-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:[J.jsx(y,{className:"w-4 h-4 inline mr-2"}),"图表分析"]}),J.jsxs("button",{onClick:()=>n("table"),className:"px-6 py-3 text-sm font-medium border-b-2 transition-colors "+("table"===r?"border-primary-600 text-primary-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:[J.jsx(I,{className:"w-4 h-4 inline mr-2"}),"数据明细"]})]})}),J.jsxs("div",{className:"p-6",children:["chart"===r&&J.jsx(Te,{klines:c,summary:i,type:"line",height:500,showToolbar:!0,loading:g,error:(null==h?void 0:h.message)||null}),"table"===r&&J.jsx(Ee,{klines:c,summary:i,pageSize:20,showSearch:!0,showExport:!0})]})]})]}):J.jsx("div",{className:`bg-white rounded-lg border p-8 ${a}`,children:J.jsxs("div",{className:"text-center",children:[J.jsx("div",{className:"text-gray-400 mb-4",children:J.jsx(y,{className:"w-12 h-12 mx-auto"})}),J.jsx("p",{className:"text-gray-600 font-medium",children:"暂无数据"}),J.jsxs("p",{className:"text-gray-500 text-sm mt-1",children:["股票代码 ",t," 暂无资金流向数据"]})]})})},Oe=({selectedStock:t,onStockSelect:s,maxTabs:a=8,className:r=""})=>{const{stocks:l,removeStock:n}=Ae(),[i,c]=e.useState(!1),[o,m]=e.useState(!1),[x,h]=e.useState(!1),u=e.useRef(null),g=()=>{const e=u.current;if(!e)return;const{scrollLeft:t,scrollWidth:s,clientWidth:a}=e;m(t>0),h(t<s-a-1),c(s>a)},f=e=>{const t=u.current;if(!t)return;const s="left"===e?t.scrollLeft-200:t.scrollLeft+200;t.scrollTo({left:s,behavior:"smooth"})};e.useEffect(()=>{const e=u.current;if(!e)return;const t=new ResizeObserver(g);return t.observe(e),e.addEventListener("scroll",g),g(),()=>{t.disconnect(),e.removeEventListener("scroll",g)}},[l]),e.useEffect(()=>{(()=>{if(!t)return;const e=u.current,s=null==e?void 0:e.querySelector(`[data-stock-code="${t}"]`);if(e&&s){const t=e.getBoundingClientRect(),a=s.getBoundingClientRect();(a.left<t.left||a.right>t.right)&&s.scrollIntoView({behavior:"smooth",block:"nearest",inline:"center"})}})()},[t]);const p=l.slice(0,a),y=l.length>a;return 0===l.length?null:J.jsx("div",{className:`bg-white border-b border-gray-200 ${r}`,children:J.jsxs("div",{className:"flex items-center",children:[i&&J.jsx("button",{onClick:()=>f("left"),disabled:!o,className:"flex-shrink-0 p-2 text-gray-400 hover:text-gray-600 disabled:opacity-30 disabled:cursor-not-allowed","aria-label":"向左滚动",children:J.jsx(L,{className:"w-4 h-4"})}),J.jsx("div",{ref:u,className:"flex-1 flex overflow-x-auto scrollbar-hide",style:{scrollbarWidth:"none",msOverflowStyle:"none"},children:J.jsxs("div",{className:"flex",children:[p.map(e=>{const a=t===e.code;return J.jsx("button",{"data-stock-code":e.code,onClick:()=>s(e.code),className:`\n                    flex-shrink-0 group relative px-4 py-3 text-sm font-medium border-b-2 transition-colors\n                    ${a?"border-primary-600 text-primary-600 bg-primary-50":"border-transparent text-gray-600 hover:text-gray-900 hover:border-gray-300"}\n                  `,children:J.jsxs("div",{className:"flex items-center gap-2 max-w-32",children:[J.jsxs("div",{className:"truncate",children:[J.jsx("div",{className:"text-xs text-gray-500 truncate",children:e.name}),J.jsx("div",{className:"font-mono text-xs",children:e.code})]}),J.jsx("button",{onClick:a=>(async(e,a)=>{a.stopPropagation();try{if(await n(e),t===e){const t=l.findIndex(t=>t.code===e),a=l[t+1]||l[t-1];s(a?a.code:"")}}catch(r){}})(e.code,a),className:"opacity-0 group-hover:opacity-100 p-1 text-gray-400 hover:text-red-500 transition-all","aria-label":`移除 ${e.name}`,children:J.jsx(T,{className:"w-3 h-3"})})]})},e.code)}),y&&J.jsx("div",{className:"flex-shrink-0 px-3 py-3 text-xs text-gray-500 border-b-2 border-transparent",children:J.jsxs("div",{className:"flex items-center gap-1",children:[J.jsx(d,{className:"w-3 h-3"}),J.jsxs("span",{children:[l.length-a," 更多"]})]})})]})}),i&&J.jsx("button",{onClick:()=>f("right"),disabled:!x,className:"flex-shrink-0 p-2 text-gray-400 hover:text-gray-600 disabled:opacity-30 disabled:cursor-not-allowed","aria-label":"向右滚动",children:J.jsx(E,{className:"w-4 h-4"})})]})})},Pe={enabled:!0,interval:6e4,onlyWhenVisible:!0,stopOnError:!1};function _e(t={},s=[]){const a={...Pe,...t},l=r(),[n,i]=e.useState(a.enabled),[c,o]=e.useState(a.interval),[d,m]=e.useState(0),x=e.useRef(null),h=e.useRef(null),u=e.useRef(0),g=e.useCallback(()=>!a.onlyWhenVisible||!document.hidden,[a.onlyWhenVisible]),f=e.useCallback(()=>{if(navigator.onLine&&g())try{s.length>0?s.forEach(e=>{l.invalidateQueries(e)}):l.invalidateQueries(Y.STOCKS),u.current=Date.now()}catch(e){a.stopOnError&&i(!1)}},[l,s,g,a.stopOnError]),p=e.useCallback(()=>{h.current&&clearInterval(h.current);const e=Date.now();h.current=window.setInterval(()=>{const t=Date.now()-e,s=Math.max(0,c-t);m(Math.ceil(s/1e3)),s<=0&&(clearInterval(h.current),h.current=null)},1e3)},[c]),y=e.useCallback(()=>{x.current&&clearInterval(x.current),!n||c<=0||(f(),p(),x.current=window.setInterval(()=>{f(),p()},c))},[n,c,f,p]),b=e.useCallback(()=>{x.current&&(clearInterval(x.current),x.current=null),h.current&&(clearInterval(h.current),h.current=null),m(0)},[]),j=e.useCallback(()=>{i(!0)},[]),N=e.useCallback(()=>{i(!1)},[]),v=e.useCallback(e=>{e<1e3||o(e)},[]);return e.useEffect(()=>(n?y():b(),()=>{b()}),[n,y,b]),e.useEffect(()=>{if(!a.onlyWhenVisible)return;const e=()=>{document.hidden?b():n&&y()};return document.addEventListener("visibilitychange",e),()=>{document.removeEventListener("visibilitychange",e)}},[a.onlyWhenVisible,n,y,b]),e.useEffect(()=>{const e=()=>{n&&y()},t=()=>{b()};return window.addEventListener("online",e),window.addEventListener("offline",t),()=>{window.removeEventListener("online",e),window.removeEventListener("offline",t)}},[n,y,b]),e.useEffect(()=>()=>{b()},[b]),{isEnabled:n,interval:c,nextRefreshIn:d,enable:j,disable:N,setInterval:v,refresh:f}}const Fe={async getStatus(){const e=await fetch("/api/cron/status"),t=await e.json();if(!t.success)throw new Error(t.message||"Failed to get cron status");return t.data},async triggerTask(){const e=await fetch("/api/cron/trigger",{method:"POST"}),t=await e.json();if(!t.success)throw new Error(t.message||"Failed to trigger task");return t.data}},ze=({className:t=""})=>{const[s,a]=e.useState(!1),i=r(),{data:c,isLoading:o,error:d,refetch:m}=l("cronStatus",Fe.getStatus,{refetchInterval:3e4,refetchOnWindowFocus:!1}),x=n(Fe.triggerTask,{onSuccess:()=>{i.invalidateQueries("cronStatus"),i.invalidateQueries(["stockData"])}}),h=(null==c?void 0:c.cacheStats)&&c.cacheStats.hits+c.cacheStats.misses>0?(c.cacheStats.hits/(c.cacheStats.hits+c.cacheStats.misses)*100).toFixed(1):"0.0";return o?J.jsx("div",{className:`bg-white rounded-lg border p-4 ${t}`,children:J.jsxs("div",{className:"flex items-center gap-3",children:[J.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-primary-600"}),J.jsx("span",{className:"text-gray-600",children:"加载定时任务状态..."})]})}):d?J.jsx("div",{className:`bg-white rounded-lg border p-4 ${t}`,children:J.jsxs("div",{className:"flex items-center gap-3 text-red-600",children:[J.jsx(p,{className:"w-5 h-5"}),J.jsx("span",{children:"无法获取定时任务状态"}),J.jsx("button",{onClick:()=>m(),className:"ml-auto p-1 hover:bg-red-50 rounded",title:"重试",children:J.jsx(k,{className:"w-4 h-4"})})]})}):J.jsxs("div",{className:`bg-white rounded-lg border ${t}`,children:[J.jsx("div",{className:"p-4 border-b",children:J.jsxs("div",{className:"flex items-center justify-between",children:[J.jsxs("div",{className:"flex items-center gap-3",children:[J.jsx("div",{className:"p-2 rounded-lg "+((null==c?void 0:c.enabled)?"bg-green-100":"bg-gray-100"),children:J.jsx(A,{className:"w-5 h-5 "+((null==c?void 0:c.enabled)?"text-green-600":"text-gray-500")})}),J.jsxs("div",{children:[J.jsx("h3",{className:"font-semibold text-gray-900",children:"定时任务"}),J.jsxs("p",{className:"text-sm text-gray-500",children:[(null==c?void 0:c.enabled)?"运行中":"已停用"," •",(null==c?void 0:c.lastExecution)?`上次执行: ${be(c.lastExecution)}`:"未执行过"]})]})]}),J.jsxs("div",{className:"flex items-center gap-2",children:[J.jsxs("button",{onClick:()=>x.mutate(),disabled:x.isLoading||!(null==c?void 0:c.enabled),className:"flex items-center gap-2 px-3 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed text-sm",title:"手动触发任务",children:[x.isLoading?J.jsx(k,{className:"w-4 h-4 animate-spin"}):J.jsx($,{className:"w-4 h-4"}),"触发"]}),J.jsx("button",{onClick:()=>a(!s),className:"p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg",title:s?"收起详情":"展开详情",children:J.jsx(k,{className:"w-4 h-4 transition-transform "+(s?"rotate-180":"")})})]})]})}),J.jsx("div",{className:"p-4",children:J.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[J.jsxs("div",{className:"text-center",children:[J.jsx("div",{className:"text-lg font-semibold text-gray-900",children:(null==c?void 0:c.config.batchSize)||0}),J.jsx("div",{className:"text-xs text-gray-500",children:"批处理大小"})]}),J.jsxs("div",{className:"text-center",children:[J.jsxs("div",{className:"text-lg font-semibold text-gray-900",children:[(null==c?void 0:c.config.cacheTtl)||0,"s"]}),J.jsx("div",{className:"text-xs text-gray-500",children:"缓存时间"})]}),J.jsxs("div",{className:"text-center",children:[J.jsxs("div",{className:"text-lg font-semibold text-green-600",children:[h,"%"]}),J.jsx("div",{className:"text-xs text-gray-500",children:"缓存命中率"})]}),J.jsxs("div",{className:"text-center",children:[J.jsx("div",{className:"text-lg font-semibold text-gray-900",children:ye((null==c?void 0:c.cacheStats.gets)||0,0)}),J.jsx("div",{className:"text-xs text-gray-500",children:"缓存请求"})]})]})}),s&&J.jsx("div",{className:"border-t bg-gray-50 p-4",children:J.jsxs("div",{className:"space-y-4",children:[J.jsxs("div",{children:[J.jsx("h4",{className:"text-sm font-medium text-gray-900 mb-2",children:"配置信息"}),J.jsxs("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[J.jsxs("div",{className:"flex justify-between",children:[J.jsx("span",{className:"text-gray-500",children:"最大重试次数:"}),J.jsx("span",{className:"font-medium",children:null==c?void 0:c.config.maxRetries})]}),J.jsxs("div",{className:"flex justify-between",children:[J.jsx("span",{className:"text-gray-500",children:"日志级别:"}),J.jsx("span",{className:"font-medium",children:null==c?void 0:c.config.logLevel})]})]})]}),J.jsxs("div",{children:[J.jsx("h4",{className:"text-sm font-medium text-gray-900 mb-2",children:"缓存统计"}),J.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4 text-sm",children:[J.jsxs("div",{className:"flex justify-between",children:[J.jsx("span",{className:"text-gray-500",children:"命中:"}),J.jsx("span",{className:"font-medium text-green-600",children:ye((null==c?void 0:c.cacheStats.hits)||0,0)})]}),J.jsxs("div",{className:"flex justify-between",children:[J.jsx("span",{className:"text-gray-500",children:"未命中:"}),J.jsx("span",{className:"font-medium text-red-600",children:ye((null==c?void 0:c.cacheStats.misses)||0,0)})]}),J.jsxs("div",{className:"flex justify-between",children:[J.jsx("span",{className:"text-gray-500",children:"写入:"}),J.jsx("span",{className:"font-medium text-blue-600",children:ye((null==c?void 0:c.cacheStats.sets)||0,0)})]})]})]}),x.data&&J.jsxs("div",{children:[J.jsx("h4",{className:"text-sm font-medium text-gray-900 mb-2",children:"最近执行结果"}),J.jsxs("div",{className:"bg-white rounded border p-3",children:[J.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[x.data.success?J.jsx(f,{className:"w-4 h-4 text-green-500"}):J.jsx(p,{className:"w-4 h-4 text-red-500"}),J.jsx("span",{className:"text-sm font-medium",children:x.data.success?"执行成功":"执行失败"}),J.jsxs("span",{className:"text-xs text-gray-500",children:["耗时 ",x.data.duration,"ms"]})]}),J.jsxs("div",{className:"text-sm text-gray-600",children:["处理: ",x.data.processed," 个股票， 错误: ",x.data.errors," 个"]})]})]})]})})]})},Ve=({showMenuButton:t=!1,onMenuClick:a,isDark:r=!1,onThemeToggle:n,className:i=""})=>{const[c,o]=s.useState(navigator.onLine),[d,m]=e.useState(!1),{isHealthy:x,canMakeRequest:h,isLoading:u}=function(){var e,t;const s=l(Y.API_STATUS,oe.getServiceStatus,ee.API_STATUS),a=s.data,r=(null==a?void 0:a.isHealthy)||!1,n=(null==(e=null==a?void 0:a.rateLimitStatus)?void 0:e.canMakeRequest)||!1,i=(null==(t=null==a?void 0:a.rateLimitStatus)?void 0:t.nextAvailableTime)||0;return{...s,data:s.data,isHealthy:r,canMakeRequest:n,nextAvailableTime:i,isLoading:s.isLoading,isError:s.isError,isSuccess:s.isSuccess,isFetching:s.isFetching,isRefetching:s.isRefetching,error:s.error,refetch:s.refetch,remove:s.remove}}(),f=function(e={}){return _e(e,[Y.STOCKS])}({enabled:!0,interval:6e4,onlyWhenVisible:!0});return s.useEffect(()=>{const e=()=>o(!0),t=()=>o(!1);return window.addEventListener("online",e),window.addEventListener("offline",t),()=>{window.removeEventListener("online",e),window.removeEventListener("offline",t)}},[]),J.jsxs("header",{className:`bg-white border-b border-gray-200 px-4 py-3 ${i}`,children:[J.jsxs("div",{className:"flex items-center justify-between",children:[J.jsxs("div",{className:"flex items-center gap-4",children:[t&&J.jsx("button",{onClick:a,className:"lg:hidden p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors","aria-label":"打开菜单",children:J.jsx(M,{className:"w-5 h-5"})}),J.jsxs("div",{className:"flex items-center gap-3",children:[J.jsx("div",{className:"flex items-center justify-center w-8 h-8 bg-primary-600 rounded-lg",children:J.jsx(R,{className:"w-5 h-5 text-white"})}),J.jsxs("div",{children:[J.jsx("h1",{className:"text-xl font-bold text-gray-900",children:"股票资金流向监控"}),J.jsx("p",{className:"text-xs text-gray-500 hidden sm:block",children:"实时监控股票资金流向数据"})]})]})]}),J.jsxs("div",{className:"flex items-center gap-3",children:[J.jsxs("div",{className:"flex items-center gap-2",children:[J.jsxs("div",{className:"flex items-center gap-1",title:c?"网络连接正常":"网络连接断开",children:[c?J.jsx(D,{className:"w-4 h-4 text-green-500"}):J.jsx(O,{className:"w-4 h-4 text-red-500"}),J.jsx("span",{className:"text-xs text-gray-500 hidden sm:inline",children:c?"在线":"离线"})]}),J.jsxs("div",{className:"flex items-center gap-1",title:x?"API服务正常":"API服务异常",children:[u?J.jsx("div",{className:"w-2 h-2 bg-yellow-500 rounded-full animate-pulse"}):J.jsx("div",{className:"w-2 h-2 rounded-full "+(x?"bg-green-500":"bg-red-500")}),J.jsx("span",{className:"text-xs text-gray-500 hidden sm:inline",children:"API"})]}),f.isEnabled&&J.jsxs("div",{className:"flex items-center gap-1 text-xs text-gray-500",children:[J.jsx(k,{className:"w-3 h-3"}),J.jsxs("span",{className:"hidden sm:inline",children:[f.nextRefreshIn,"s"]})]})]}),J.jsx("div",{className:"w-px h-4 bg-gray-300"}),J.jsxs("div",{className:"flex items-center gap-1",children:[J.jsx("button",{onClick:()=>f.refresh(),disabled:!c||!h,className:"p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed",title:"手动刷新数据",children:J.jsx(k,{className:"w-4 h-4"})}),J.jsx("button",{onClick:()=>f.isEnabled?f.disable():f.enable(),className:"p-2 rounded-lg transition-colors "+(f.isEnabled?"text-primary-600 bg-primary-50 hover:bg-primary-100":"text-gray-600 hover:text-gray-900 hover:bg-gray-100"),title:f.isEnabled?"停止自动刷新":"启动自动刷新",children:J.jsx(k,{className:"w-4 h-4 "+(f.isEnabled?"animate-spin":"")})}),n&&J.jsx("button",{onClick:n,className:"p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors",title:r?"切换到亮色主题":"切换到暗色主题",children:r?J.jsx(P,{className:"w-4 h-4"}):J.jsx(_,{className:"w-4 h-4"})}),J.jsxs("div",{className:"relative",children:[J.jsx("button",{onClick:()=>m(!d),className:"p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors",title:"定时任务状态",children:J.jsx(A,{className:"w-4 h-4"})}),d&&J.jsxs(J.Fragment,{children:[J.jsx("div",{className:"fixed inset-0 z-40",onClick:()=>m(!1)}),J.jsx("div",{className:"absolute right-0 top-full mt-2 w-96 z-50",children:J.jsx(ze,{})})]})]}),J.jsx("button",{className:"p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors",title:"设置",children:J.jsx(g,{className:"w-4 h-4"})})]})]})]}),!h&&J.jsx("div",{className:"mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded-lg",children:J.jsxs("div",{className:"flex items-center gap-2 text-yellow-700 text-sm",children:[J.jsx(k,{className:"w-4 h-4"}),J.jsx("span",{children:"API请求频率限制中，请稍后再试"})]})})]})},Be=({selectedStock:t,onStockSelect:s,isOpen:a=!0,onClose:r,className:l=""})=>{const{stocks:n,error:i}=Ae(),[c,o]=e.useState(!0);return J.jsxs(J.Fragment,{children:[a&&r&&J.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden",onClick:r}),J.jsxs("aside",{className:`\n          fixed lg:static inset-y-0 left-0 z-50 lg:z-auto\n          w-80 bg-white border-r border-gray-200\n          transform transition-transform duration-300 ease-in-out\n          ${a?"translate-x-0":"-translate-x-full lg:translate-x-0"}\n          ${l}\n        `,children:[J.jsxs("div",{className:"flex items-center justify-between p-4 border-b border-gray-200 lg:hidden",children:[J.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:"股票管理"}),r&&J.jsx("button",{onClick:r,className:"p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors","aria-label":"关闭菜单",children:J.jsx(T,{className:"w-5 h-5"})})]}),J.jsxs("div",{className:"flex flex-col h-full lg:h-auto",children:[J.jsx("div",{className:"flex-1 p-4",children:J.jsx(he,{selectedStock:t,onSelectStock:s})}),J.jsx("div",{className:"border-t border-gray-200 bg-gray-50",children:J.jsxs("div",{className:"p-4",children:[J.jsxs("div",{className:"flex items-center justify-between mb-3",children:[J.jsxs("div",{className:"flex items-center gap-2",children:[J.jsx(R,{className:"w-4 h-4 text-blue-600"}),J.jsx("h3",{className:"text-sm font-medium text-gray-900",children:"实时监控"}),J.jsxs("span",{className:"text-xs text-gray-500",children:["(",n.length,"只)"]})]}),J.jsx("button",{onClick:()=>{o(!c)},className:"p-1 hover:bg-gray-200 rounded transition-colors",title:c?"收起监控面板":"展开监控面板",children:c?J.jsx(w,{className:"w-4 h-4 text-gray-600"}):J.jsx(S,{className:"w-4 h-4 text-gray-600"})})]}),t&&J.jsx("div",{className:"mb-3 p-2 bg-blue-50 border border-blue-200 rounded text-xs",children:J.jsxs("div",{className:"flex items-center gap-2 text-blue-700",children:[J.jsx(F,{className:"w-3 h-3"}),J.jsxs("span",{children:["当前选中: ",t]})]})}),i&&J.jsxs("div",{className:"mb-3 p-2 bg-red-50 border border-red-200 rounded text-xs text-red-600",children:["加载失败: ",i.message]}),c&&J.jsx("div",{className:"space-y-2",children:J.jsx(He,{compact:!0,maxItems:15,onStockClick:e=>{s(e)},className:"max-h-80 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100"})}),!c&&n.length>0&&J.jsxs("div",{className:"text-xs text-gray-500 text-center py-2",children:["点击展开查看 ",n.length," 只股票的实时监控"]})]})})]})]})]})},Ke=({selectedStock:e,className:t=""})=>{var s;const{stocks:a,isLoading:r,error:l}=Ae();return J.jsx("main",{className:`flex-1 overflow-auto ${t}`,children:J.jsx("div",{className:"p-4 lg:p-6",children:e?J.jsxs(J.Fragment,{children:[J.jsx("div",{className:"mb-6",children:J.jsxs("nav",{className:"flex items-center space-x-2 text-sm text-gray-500",children:[J.jsx("span",{children:"股票监控"}),J.jsx("span",{children:"/"}),J.jsx("span",{className:"text-gray-900 font-medium",children:(null==(s=a.find(t=>t.code===e))?void 0:s.name)||e}),J.jsx("span",{className:"text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded",children:e})]})}),J.jsx(De,{stockCode:e,limit:240})]}):J.jsx("div",{className:"bg-white rounded-lg border",children:r?J.jsxs("div",{className:"flex flex-col items-center justify-center h-96 text-center",children:[J.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mb-4"}),J.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"正在加载股票列表..."}),J.jsx("p",{className:"text-gray-500",children:"请稍候，正在获取您的股票监控列表"})]}):l?J.jsxs("div",{className:"flex flex-col items-center justify-center h-96 text-center",children:[J.jsx("div",{className:"text-red-500 mb-4",children:J.jsx(o,{className:"w-16 h-16 mx-auto"})}),J.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"加载失败"}),J.jsxs("p",{className:"text-gray-500 mb-4",children:["无法加载股票列表: ",l.message]}),J.jsx("button",{onClick:()=>window.location.reload(),className:"btn btn-primary",children:"重新加载"})]}):0===a.length?J.jsxs("div",{className:"flex flex-col items-center justify-center h-96 text-center",children:[J.jsx("div",{className:"text-gray-400 mb-6",children:J.jsx(m,{className:"w-20 h-20 mx-auto"})}),J.jsx("h3",{className:"text-xl font-medium text-gray-900 mb-3",children:"开始监控股票资金流向"}),J.jsx("p",{className:"text-gray-500 mb-6 max-w-md",children:"您还没有添加任何股票到监控列表。请在左侧添加股票代码，开始监控资金流向数据。"}),J.jsxs("div",{className:"space-y-3 text-sm text-gray-600",children:[J.jsxs("div",{className:"flex items-center gap-2",children:[J.jsx("div",{className:"w-2 h-2 bg-primary-600 rounded-full"}),J.jsx("span",{children:"支持沪深两市股票代码"})]}),J.jsxs("div",{className:"flex items-center gap-2",children:[J.jsx("div",{className:"w-2 h-2 bg-primary-600 rounded-full"}),J.jsx("span",{children:"实时资金流向数据"})]}),J.jsxs("div",{className:"flex items-center gap-2",children:[J.jsx("div",{className:"w-2 h-2 bg-primary-600 rounded-full"}),J.jsx("span",{children:"专业图表分析"})]})]})]}):J.jsxs("div",{className:"flex flex-col items-center justify-center h-96 text-center",children:[J.jsx("div",{className:"text-gray-400 mb-6",children:J.jsx(y,{className:"w-20 h-20 mx-auto"})}),J.jsx("h3",{className:"text-xl font-medium text-gray-900 mb-3",children:"选择股票查看数据"}),J.jsxs("p",{className:"text-gray-500 mb-4 max-w-md",children:["您已添加 ",a.length," 只股票到监控列表。请在左侧选择一只股票，查看详细的资金流向数据和图表分析。"]}),J.jsxs("div",{className:"text-sm text-gray-600",children:[J.jsx("p",{children:"监控列表中的股票:"}),J.jsxs("div",{className:"mt-2 flex flex-wrap gap-2 justify-center",children:[a.slice(0,5).map(e=>J.jsxs("span",{className:"px-2 py-1 bg-gray-100 text-gray-700 rounded text-xs",children:[e.name," (",e.code,")"]},e.code)),a.length>5&&J.jsxs("span",{className:"px-2 py-1 bg-gray-100 text-gray-700 rounded text-xs",children:["+",a.length-5," 更多"]})]})]})]})})})})},We=({children:t})=>{const[s,a]=e.useState(null),[r,l]=e.useState(!1),[n,i]=e.useState(!1),[c,o]=e.useState(!1),{stocks:d}=Ae();e.useEffect(()=>{const e=()=>{o(window.innerWidth<1024)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),e.useEffect(()=>{d.length>0&&!s&&a(d[0].code)},[d,s]),e.useEffect(()=>{c||l(!1)},[c]);const m=e=>{a(e),c&&l(!1)};return J.jsxs("div",{className:"min-h-screen bg-gray-50 "+(n?"dark":""),children:[J.jsx(Ve,{showMenuButton:c,onMenuClick:()=>l(!0),isDark:n,onThemeToggle:()=>{i(!n),document.documentElement.classList.toggle("dark",!n)}}),J.jsxs("div",{className:"flex h-[calc(100vh-64px)]",children:[J.jsx(Be,{selectedStock:s,onStockSelect:m,isOpen:!c||r,onClose:c?()=>l(!1):void 0}),J.jsxs("div",{className:"flex-1 flex flex-col overflow-hidden",children:[d.length>0&&J.jsx(Oe,{selectedStock:s,onStockSelect:m,maxTabs:c?3:8}),t||J.jsx(Ke,{selectedStock:s})]})]})]})},Ue=({stock:e,data:t,hasVPattern:s=!1,onClick:a,compact:r=!1})=>{var l,n;const i=(null==(n=null==(l=null==t?void 0:t.klines)?void 0:l[t.klines.length-1])?void 0:n.mainNetInflow)||0,c=(null==t?void 0:t.klines)&&t.klines.length>=2?t.klines[t.klines.length-1].mainNetInflow-t.klines[t.klines.length-2].mainNetInflow:0,o=()=>{a&&a(e.code)};return t&&t.klines&&0!==t.klines.length?J.jsx("div",{className:`bg-white border rounded-lg ${r?"p-2":"p-3"} transition-all duration-200 ${s?"border-red-200 bg-red-50 shadow-md":"border-gray-200 hover:border-gray-300"} ${a?"cursor-pointer hover:shadow-sm":""}`,onClick:o,children:J.jsxs("div",{className:"flex items-center gap-3",children:[J.jsxs("div",{className:"flex-1 min-w-0",children:[J.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[J.jsx("span",{className:"font-medium text-gray-900 "+(r?"text-sm":"text-base"),children:e.name}),J.jsx("span",{className:"text-gray-500 "+(r?"text-xs":"text-sm"),children:e.code}),s&&J.jsxs("div",{className:"flex items-center gap-1",children:[J.jsx(R,{className:"w-3 h-3 text-red-500"}),J.jsx("span",{className:"text-xs text-red-600 font-medium",children:"V型"})]})]}),J.jsxs("div",{className:"flex items-center gap-3",children:[J.jsxs("div",{className:"flex items-center gap-1",children:[J.jsx("span",{className:"text-xs text-gray-500 "+(r?"hidden":""),children:"主力净流入"}),J.jsx("span",{className:"font-bold "+(r?"text-sm":"text-base"),style:{color:pe(i)},children:ge(i)})]}),0!==c&&J.jsxs("div",{className:"flex items-center gap-1",children:[c>0?J.jsx(m,{className:"w-3 h-3 text-red-500"}):J.jsx(C,{className:"w-3 h-3 text-green-500"}),J.jsxs("span",{className:"text-xs font-medium "+(c>0?"text-red-600":"text-green-600"),children:[c>0?"+":"",ge(c)]})]})]})]}),J.jsx("div",{className:(r?"w-16 h-8":"w-20 h-10")+" flex-shrink-0",children:J.jsx(Re,{klines:t.klines,height:r?32:40,showVPattern:!0})})]})}):J.jsx("div",{className:`bg-white border rounded-lg p-3 ${r?"p-2":"p-3"} ${a?"cursor-pointer hover:bg-gray-50":""}`,onClick:o,children:J.jsxs("div",{className:"flex items-center justify-between",children:[J.jsx("div",{className:"flex-1 min-w-0",children:J.jsxs("div",{className:"flex items-center gap-2",children:[J.jsx("span",{className:"font-medium text-gray-900 "+(r?"text-sm":"text-base"),children:e.name}),J.jsx("span",{className:"text-gray-500 "+(r?"text-xs":"text-sm"),children:e.code})]})}),J.jsx("div",{className:"text-gray-400 text-sm",children:"暂无数据"})]})})},He=({compact:t=!1,maxItems:s=20,onStockClick:a,className:r=""})=>{const[n,i]=e.useState("vPattern"),{stocks:c}=le(),d=c.map(e=>e.code),{results:x,isLoading:h,isError:u,error:g,refetch:f,isFetching:p,successCount:b,errorCount:j}=function(e,t=240,s={}){const{enabled:a=!0,useCache:r=!0,refetchInterval:n}=s,i=l(Y.STOCK_DATA_BATCH(e),()=>oe.getBatchStockData(e,t,r),{...ee.STOCK_DATA_BATCH,enabled:a&&e.length>0,refetchInterval:n??ee.STOCK_DATA_BATCH.refetchInterval}),c=i.data,o=(null==c?void 0:c.results)||{},d=(null==c?void 0:c.errors)||{},m=(null==c?void 0:c.summary)||{success:0,failed:0,fromCache:0};return{...i,data:i.data,results:o,errors:d,successCount:m.success,errorCount:m.failed,fromCacheCount:m.fromCache,isLoading:i.isLoading,isError:i.isError,isSuccess:i.isSuccess,isFetching:i.isFetching,isRefetching:i.isRefetching,error:i.error,refetch:i.refetch,remove:i.remove}}(d,20,{refetchInterval:6e4,enabled:d.length>0}),N=e.useMemo(()=>{if(!x||0===Object.keys(x).length)return[];return c.map(e=>{var t,s;const a=x[e.code],r=!!(null==a?void 0:a.klines)&&Me(a.klines).hasVPattern,l=(null==(s=null==(t=null==a?void 0:a.klines)?void 0:t[a.klines.length-1])?void 0:s.mainNetInflow)||0,n=(null==a?void 0:a.klines)&&a.klines.length>=2?a.klines[a.klines.length-1].mainNetInflow-a.klines[a.klines.length-2].mainNetInflow:0;return{...e,data:a,hasVPattern:r,latestFlow:l,change24h:n}}).filter(e=>e.data).sort((e,t)=>{switch(n){case"vPattern":return e.hasVPattern&&!t.hasVPattern?-1:!e.hasVPattern&&t.hasVPattern?1:Math.abs(t.latestFlow)-Math.abs(e.latestFlow);case"flow":return Math.abs(t.latestFlow)-Math.abs(e.latestFlow);case"change":return Math.abs(t.change24h)-Math.abs(e.change24h);default:return 0}}).slice(0,s)},[x,c,n,s]),v=e.useMemo(()=>{const e=N.filter(e=>e.hasVPattern).length,t=N.filter(e=>e.latestFlow>0).length,s=N.length;return{total:s,vPattern:e,positiveFlow:t,negativeFlow:s-t}},[N]),w=()=>{f()};return h&&d.length>0?J.jsx("div",{className:`space-y-3 ${r}`,children:J.jsx("div",{className:"flex items-center justify-center py-8",children:J.jsxs("div",{className:"text-center",children:[J.jsx(k,{className:"w-6 h-6 animate-spin text-blue-500 mx-auto mb-2"}),J.jsx("p",{className:"text-sm text-gray-600",children:"正在加载监控数据..."}),J.jsxs("p",{className:"text-xs text-gray-500 mt-1",children:[d.length," 只股票"]})]})})}):u?J.jsx("div",{className:`space-y-3 ${r}`,children:J.jsx("div",{className:"flex items-center justify-center py-8",children:J.jsxs("div",{className:"text-center",children:[J.jsx(o,{className:"w-6 h-6 text-red-500 mx-auto mb-2"}),J.jsx("p",{className:"text-sm text-red-600 mb-2",children:"监控数据加载失败"}),J.jsx("p",{className:"text-xs text-gray-500 mb-3",children:(null==g?void 0:g.message)||"网络连接异常"}),J.jsx("button",{onClick:w,className:"text-xs bg-blue-500 text-white px-3 py-1 rounded hover:bg-blue-600 transition-colors",children:"重新加载"})]})})}):0===d.length?J.jsx("div",{className:`space-y-3 ${r}`,children:J.jsx("div",{className:"flex items-center justify-center py-8",children:J.jsxs("div",{className:"text-center",children:[J.jsx(y,{className:"w-6 h-6 text-gray-400 mx-auto mb-2"}),J.jsx("p",{className:"text-sm text-gray-600",children:"暂无监控股票"}),J.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"请先添加股票到监控列表"})]})})}):J.jsxs("div",{className:`space-y-3 ${r}`,children:[J.jsxs("div",{className:"flex items-center justify-between",children:[J.jsxs("div",{className:"flex items-center gap-2",children:[J.jsx("h3",{className:"font-medium text-gray-900 "+(t?"text-sm":"text-base"),children:"实时监控"}),p&&J.jsx(k,{className:"w-3 h-3 animate-spin text-blue-500"})]}),J.jsx("button",{onClick:w,className:"text-xs text-gray-500 hover:text-gray-700 transition-colors",disabled:p,children:"刷新"})]}),!t&&J.jsxs("div",{className:"grid grid-cols-4 gap-2 text-center",children:[J.jsxs("div",{className:"bg-gray-50 rounded p-2",children:[J.jsx("div",{className:"text-xs text-gray-500",children:"总计"}),J.jsx("div",{className:"text-sm font-bold text-gray-900",children:v.total})]}),J.jsxs("div",{className:"bg-red-50 rounded p-2",children:[J.jsxs("div",{className:"text-xs text-red-600 flex items-center justify-center gap-1",children:[J.jsx(R,{className:"w-3 h-3"}),"V型"]}),J.jsx("div",{className:"text-sm font-bold text-red-600",children:v.vPattern})]}),J.jsxs("div",{className:"bg-green-50 rounded p-2",children:[J.jsxs("div",{className:"text-xs text-green-600 flex items-center justify-center gap-1",children:[J.jsx(m,{className:"w-3 h-3"}),"流入"]}),J.jsx("div",{className:"text-sm font-bold text-green-600",children:v.positiveFlow})]}),J.jsxs("div",{className:"bg-blue-50 rounded p-2",children:[J.jsx("div",{className:"text-xs text-blue-600",children:"成功"}),J.jsx("div",{className:"text-sm font-bold text-blue-600",children:b})]})]}),!t&&N.length>0&&J.jsx("div",{className:"flex gap-1",children:[{key:"vPattern",label:"V型优先"},{key:"flow",label:"资金流入"},{key:"change",label:"24h变化"}].map(({key:e,label:t})=>J.jsx("button",{onClick:()=>{i(e)},className:"text-xs px-2 py-1 rounded transition-colors "+(n===e?"bg-blue-500 text-white":"bg-gray-100 text-gray-600 hover:bg-gray-200"),children:t},e))}),J.jsx("div",{className:"space-y-2 max-h-96 overflow-y-auto",children:N.map(e=>J.jsx(Ue,{stock:e,data:e.data,hasVPattern:e.hasVPattern,onClick:a,compact:t},e.code))}),N.length>0&&J.jsxs("div",{className:"text-xs text-gray-500 text-center pt-2 border-t",children:["显示 ",N.length," / ",d.length," 只股票",j>0&&J.jsxs("span",{className:"text-red-500 ml-2",children:["· ",j," 只加载失败"]})]})]})};function Qe(){return J.jsx(We,{})}G.createRoot(document.getElementById("root")).render(J.jsx(s.StrictMode,{children:J.jsxs(i,{client:X,children:[J.jsx(Qe,{}),!1]})}));
