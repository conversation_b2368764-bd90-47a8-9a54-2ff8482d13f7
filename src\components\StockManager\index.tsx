import { useState } from 'react';
import { Settings, CheckCircle, XCircle } from 'lucide-react';
import { useStockList } from '@/hooks/useStockList';
import { StockInput } from './StockInput';
import { StockList } from './StockList';

interface StockManagerProps {
  onSelectStock?: (code: string) => void;
  selectedStock?: string | null;
}

export function StockManager({ onSelectStock, selectedStock }: StockManagerProps) {
  const { stocks, addStock, removeStock, isLoading, error } = useStockList();
  const [notification, setNotification] = useState<{
    type: 'success' | 'error';
    message: string;
  } | null>(null);

  // 显示通知
  const showNotification = (type: 'success' | 'error', message: string) => {
    setNotification({ type, message });
    setTimeout(() => setNotification(null), 3000);
  };

  // 处理添加股票
  const handleAddStock = async (code: string, name?: string) => {
    const result = await addStock(code, name);

    if (result.success) {
      showNotification('success', result.message || '股票添加成功');
    } else {
      showNotification('error', result.message || '添加失败');
    }

    return result;
  };

  // 处理删除股票
  const handleRemoveStock = (code: string) => {
    removeStock(code);
    showNotification('success', '股票删除成功');
    
    // 如果删除的是当前选中的股票，清除选中状态
    if (selectedStock === code && onSelectStock) {
      onSelectStock('');
    }
  };

  return (
    <div className="card p-6 h-full">
      {/* 头部 */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">股票管理</h2>
          <p className="text-sm text-gray-600 mt-1">
            添加和管理要监控的股票代码
          </p>
        </div>
        <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors">
          <Settings className="w-5 h-5" />
        </button>
      </div>

      {/* 通知栏 */}
      {notification && (
        <div className={`
          flex items-center gap-2 p-3 rounded-lg mb-4 animate-slide-up
          ${notification.type === 'success' 
            ? 'bg-success-50 text-success-700 border border-success-200' 
            : 'bg-danger-50 text-danger-700 border border-danger-200'
          }
        `}>
          {notification.type === 'success' ? (
            <CheckCircle className="w-4 h-4" />
          ) : (
            <XCircle className="w-4 h-4" />
          )}
          <span className="text-sm">{notification.message}</span>
        </div>
      )}

      {/* 错误提示 */}
      {error && (
        <div className="flex items-center gap-2 p-3 rounded-lg mb-4 bg-danger-50 text-danger-700 border border-danger-200">
          <XCircle className="w-4 h-4" />
          <span className="text-sm">{error}</span>
        </div>
      )}

      {/* 股票输入 */}
      <div className="mb-6">
        <StockInput 
          onAddStock={handleAddStock}
          isLoading={isLoading}
        />
      </div>

      {/* 股票列表 */}
      <div className="flex-1">
        <StockList
          stocks={stocks}
          onRemoveStock={handleRemoveStock}
          onSelectStock={onSelectStock}
          selectedStock={selectedStock}
        />
      </div>

      {/* 统计信息 */}
      {stocks.length > 0 && (
        <div className="mt-6 pt-4 border-t border-gray-200">
          <div className="flex items-center justify-between text-sm text-gray-600">
            <span>已添加 {stocks.length} 只股票</span>
            <span>
              {selectedStock ? `当前选中: ${selectedStock}` : '请选择股票查看数据'}
            </span>
          </div>
        </div>
      )}
    </div>
  );
}

export default StockManager;
